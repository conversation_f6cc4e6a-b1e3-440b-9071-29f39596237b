# 模块化报告系统部署和测试指南

## 概述

本指南详细说明了如何部署和测试模块化报告系统。该系统包含后端API服务、前端Web应用和数据库组件。

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+) / macOS / Windows 10+
- **Python**: 3.8+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+ (可选，用于缓存)

## 部署步骤

### 1. 环境准备

#### 1.1 克隆代码库
```bash
cd /data/app
git clone <repository-url> CEMonitor
cd CEMonitor
```

#### 1.2 创建虚拟环境
```bash
# 后端环境
cd apps/api
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 1.3 安装前端依赖
```bash
cd apps/web
npm install
# 或者使用 yarn
yarn install
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 连接到MySQL服务器
mysql -u root -p

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS agentos_online CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS speech_ai_robot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS bigdata_cn CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（如果需要）
CREATE USER IF NOT EXISTS 'cemonitor'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON agentos_online.* TO 'cemonitor'@'localhost';
GRANT ALL PRIVILEGES ON speech_ai_robot.* TO 'cemonitor'@'localhost';
GRANT ALL PRIVILEGES ON bigdata_cn.* TO 'cemonitor'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 执行数据库迁移
```bash
cd apps/api

# 执行模块化报告表创建脚本
mysql -u cemonitor -p agentos_online < create_modular_report_tables.sql

# 验证表创建
mysql -u cemonitor -p agentos_online -e "SHOW TABLES LIKE '%report%';"
```

#### 2.3 验证默认数据
```sql
-- 检查模块模板
SELECT * FROM module_template;

-- 检查数据源配置
SELECT * FROM data_source_config;
```

### 3. 配置文件设置

#### 3.1 后端配置
创建或更新 `apps/api/.env` 文件：
```env
# 数据库配置
DATABASE_URL_AGENTOS=mysql+pymysql://cemonitor:your_password@localhost:3306/agentos_online
DATABASE_URL_SPEECH=mysql+pymysql://cemonitor:your_password@localhost:3306/speech_ai_robot
DATABASE_URL_BIGDATA=mysql+pymysql://cemonitor:your_password@localhost:3306/bigdata_cn

# API配置
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False

# LLM配置
LLM_API_KEY=your_llm_api_key
LLM_BASE_URL=https://api.your-llm-provider.com

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

#### 3.2 前端配置
创建或更新 `apps/web/.env` 文件：
```env
# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MODULAR_REPORTS=true
VITE_ENABLE_CHART_EXPORT=true

# 调试配置
VITE_DEBUG=false
```

### 4. 启动服务

#### 4.1 启动后端服务
```bash
cd apps/api
source venv/bin/activate

# 开发模式
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 生产模式
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 4.2 启动前端服务
```bash
cd apps/web

# 开发模式
npm run dev
# 或者
yarn dev

# 生产构建
npm run build
npm run preview
```

### 5. 服务验证

#### 5.1 后端API验证
```bash
# 检查API健康状态
curl http://localhost:8000/health

# 检查模块类型接口
curl http://localhost:8000/device-reports/module-types

# 检查数据源接口
curl http://localhost:8000/device-reports/data-sources
```

#### 5.2 前端页面验证
访问 `http://localhost:5173` (开发模式) 或 `http://localhost:4173` (预览模式)

检查以下页面：
- 主页面加载正常
- 模块化报告页面可访问
- 图表组件正常渲染

## 测试指南

### 1. 运行自动化测试

#### 1.1 使用测试脚本
```bash
# 运行所有测试
./run_tests.sh

# 只运行后端测试
./run_tests.sh backend

# 只运行前端测试
./run_tests.sh frontend

# 只运行集成测试
./run_tests.sh integration
```

#### 1.2 手动运行测试

**后端测试**:
```bash
cd apps/api
source venv/bin/activate

# 运行所有测试
pytest tests/ -v

# 运行特定测试文件
pytest tests/test_modular_report_service.py -v

# 生成覆盖率报告
pytest tests/ --cov=app --cov-report=html
```

**前端测试**:
```bash
cd apps/web

# 运行所有测试
npm run test

# 运行特定测试文件
npm run test src/tests/ModularReportPage.test.tsx

# 生成覆盖率报告
npm run test:coverage
```

### 2. 功能测试

#### 2.1 基础功能测试
1. **模块化报告生成**:
   - 访问模块化报告页面
   - 配置企业ID和设备ID
   - 选择目标日期
   - 添加不同类型的模块
   - 启动报告生成
   - 验证实时进度更新
   - 检查生成结果

2. **图表功能测试**:
   - 验证图表正常渲染
   - 测试图表交互功能
   - 测试数据点点击
   - 测试全屏显示
   - 测试导出功能

3. **会话详情测试**:
   - 从图表点击进入会话详情
   - 验证会话信息显示
   - 测试消息列表展示
   - 测试导出功能

#### 2.2 API接口测试
使用Postman或curl测试API接口：

**创建模块化报告**:
```bash
curl -X POST "http://localhost:8000/device-reports/test_enterprise/test_device/modular" \
  -H "Content-Type: application/json" \
  -d '{
    "target_date": "2024-01-31",
    "module_configs": [
      {
        "module_type": "basic_usage",
        "module_name": "基础使用数据",
        "module_description": "测试模块",
        "data_sources": {"statistics": {}},
        "primary_data_source": "statistics",
        "execution_mode": "llm_generate",
        "use_llm": true,
        "custom_config": {},
        "prompt_variables": {}
      }
    ]
  }'
```

**获取模块模板**:
```bash
curl "http://localhost:8000/device-reports/templates"
```

**查询数据源**:
```bash
curl -X POST "http://localhost:8000/device-reports/test_enterprise/test_device/data-query" \
  -H "Content-Type: application/json" \
  -d '{
    "data_source_type": "statistics",
    "query_config": {
      "metrics": ["session_count"],
      "time_range": "1d"
    },
    "target_date": "2024-01-31"
  }'
```

### 3. 性能测试

#### 3.1 负载测试
使用Apache Bench或类似工具进行负载测试：

```bash
# 测试API响应性能
ab -n 100 -c 10 http://localhost:8000/device-reports/module-types

# 测试报告生成性能
ab -n 10 -c 2 -p test_data.json -T application/json \
  http://localhost:8000/device-reports/test_enterprise/test_device/modular
```

#### 3.2 内存和CPU监控
```bash
# 监控后端服务资源使用
top -p $(pgrep -f "uvicorn\|gunicorn")

# 监控数据库性能
mysqladmin -u cemonitor -p processlist
mysqladmin -u cemonitor -p status
```

### 4. 集成测试

#### 4.1 端到端测试流程
1. 启动所有服务（数据库、后端、前端）
2. 通过前端界面创建模块化报告
3. 验证数据库中的记录创建
4. 检查API响应和SSE流
5. 验证图表生成和交互
6. 测试会话详情查看
7. 验证导出功能

#### 4.2 数据一致性测试
```sql
-- 检查报告和模块数据一致性
SELECT 
  dr.id as report_id,
  dr.total_modules,
  dr.completed_modules,
  COUNT(rm.id) as actual_modules,
  SUM(CASE WHEN rm.status = 'completed' THEN 1 ELSE 0 END) as actual_completed
FROM device_report dr
LEFT JOIN report_module rm ON dr.id = rm.report_id
WHERE dr.is_modular = 1
GROUP BY dr.id;
```

## 故障排除

### 1. 常见问题

#### 1.1 数据库连接问题
**症状**: 无法连接到数据库
**解决方案**:
1. 检查数据库服务是否运行
2. 验证连接字符串配置
3. 检查用户权限
4. 验证网络连接

#### 1.2 API服务启动失败
**症状**: 后端服务无法启动
**解决方案**:
1. 检查端口是否被占用
2. 验证环境变量配置
3. 检查依赖包安装
4. 查看错误日志

#### 1.3 前端页面无法加载
**症状**: 前端页面显示错误或无法加载
**解决方案**:
1. 检查API服务是否正常
2. 验证前端配置
3. 检查网络连接
4. 清除浏览器缓存

### 2. 日志分析

#### 2.1 后端日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看特定模块日志
grep "modular_report" logs/app.log
```

#### 2.2 数据库日志
```bash
# 查看MySQL错误日志
tail -f /var/log/mysql/error.log

# 查看慢查询日志
tail -f /var/log/mysql/slow.log
```

#### 2.3 前端调试
在浏览器开发者工具中：
1. 检查Console错误信息
2. 查看Network请求状态
3. 检查Application存储状态

### 3. 性能优化

#### 3.1 数据库优化
```sql
-- 检查索引使用情况
EXPLAIN SELECT * FROM report_module WHERE report_id = 1;

-- 优化查询
ANALYZE TABLE device_report, report_module;

-- 检查表状态
SHOW TABLE STATUS LIKE 'report_module';
```

#### 3.2 API优化
1. 启用响应压缩
2. 配置适当的缓存策略
3. 优化数据库查询
4. 使用连接池

#### 3.3 前端优化
1. 启用代码分割
2. 优化图片和资源
3. 使用CDN加速
4. 启用浏览器缓存

## 监控和维护

### 1. 监控指标
- API响应时间
- 数据库查询性能
- 内存和CPU使用率
- 错误率和异常统计
- 用户活跃度

### 2. 定期维护
- 数据库备份
- 日志清理
- 性能分析
- 安全更新
- 配置优化

### 3. 告警设置
- 服务不可用告警
- 性能异常告警
- 错误率过高告警
- 资源使用过高告警

## 安全配置

### 1. 网络安全
- 配置防火墙规则
- 使用HTTPS加密
- 限制API访问频率
- 配置CORS策略

### 2. 数据安全
- 数据库访问控制
- 敏感数据加密
- 定期安全审计
- 备份数据保护

### 3. 应用安全
- 输入验证和过滤
- SQL注入防护
- XSS攻击防护
- 认证和授权机制
