import React, { useState, useRef, useCallback, useEffect } from 'react';
import EnhancedMarkdownRenderer from '../components/EnhancedMarkdownRenderer';
import ModuleConfigPanel from '../components/ModuleConfigPanel';
import ModuleProgressTracker from '../components/ModuleProgressTracker';
import ChartDisplayPanel from '../components/ChartDisplayPanel';
import { 
  Loader2, Play, Square, FileText, Wifi, CheckCircle2, XCircle, Clock, 
  Settings, BarChart3, Database, Layers, Brain
} from 'lucide-react';

interface ModuleConfig {
  module_type: string;
  module_name: string;
  module_description: string;
  data_sources: Record<string, any>;
  primary_data_source: string;
  execution_mode: string;
  use_llm: boolean;
  template_name?: string;
  custom_config: Record<string, any>;
  system_prompt?: string;
  user_prompt?: string;
  prompt_variables: Record<string, any>;
}

interface ModularReportFormData {
  enterpriseId: string;
  deviceId: string;
  target_date: string;
  start_date?: string;
  end_date?: string;
  module_configs: ModuleConfig[];
  model_name?: string;
}

interface ModuleProgress {
  module_index: number;
  module_name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  start_time?: string;
  end_time?: string;
  error?: string;
  content?: string;
  chart_config?: any;
}

interface SSEMessage {
  type: string;
  content?: string;
  metadata?: any;
  timestamp: string;
}

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';
type GenerationStatus = 'idle' | 'generating' | 'completed' | 'error';

// 企业设备数据配置 (复用原有配置)
interface Device {
  id: string;
  name: string;
}

interface Enterprise {
  id: string;
  name: string;
  devices: Device[];
}

const ENTERPRISE_DATA: Enterprise[] = [
  {
    id: 'orion.ovs.entprise.4498860269',
    name: '奥维视讯',
    devices: [
      { id: 'orion.ovs.device.4498860269.1', name: '设备1' },
      { id: 'orion.ovs.device.4498860269.2', name: '设备2' },
      { id: 'orion.ovs.device.4498860269.3', name: '设备3' }
    ]
  }
];

// 默认模块配置
const DEFAULT_MODULE_CONFIGS: ModuleConfig[] = [
  {
    module_type: 'basic_usage',
    module_name: '基础使用数据',
    module_description: '基础使用数据、当天数据趋势、action数据统计',
    data_sources: {
      'statistics': { include_trends: true, include_actions: true },
      'conversation': { max_records: 1000 }
    },
    primary_data_source: 'statistics',
    execution_mode: 'llm_generate',
    use_llm: true,
    custom_config: {},
    prompt_variables: {}
  },
  {
    module_type: 'event_analysis',
    module_name: '事件分析',
    module_description: '当日亮点事件和预警事件',
    data_sources: {
      'statistics': { focus_events: true },
      'conversation': { filter_events: true }
    },
    primary_data_source: 'statistics',
    execution_mode: 'llm_generate',
    use_llm: true,
    custom_config: {},
    prompt_variables: {}
  },
  {
    module_type: 'service_quality',
    module_name: '服务质量',
    module_description: '当日满意服务案例和不满意的案例',
    data_sources: {
      'conversation': { include_quality_metrics: true },
      'statistics': { include_satisfaction: true }
    },
    primary_data_source: 'conversation',
    execution_mode: 'llm_generate',
    use_llm: true,
    custom_config: {},
    prompt_variables: {}
  },
  {
    module_type: 'insight_analysis',
    module_name: '洞察分析',
    module_description: '一些新视角的观察',
    data_sources: {
      'statistics': {},
      'conversation': {},
      'human_stats': {}
    },
    primary_data_source: 'mixed',
    execution_mode: 'llm_generate',
    use_llm: true,
    custom_config: {},
    prompt_variables: {}
  },
  {
    module_type: 'business_advice',
    module_name: '业务建议',
    module_description: '提出一些经营建议',
    data_sources: {
      'statistics': {},
      'conversation': {},
      'query': {}
    },
    primary_data_source: 'mixed',
    execution_mode: 'llm_generate',
    use_llm: true,
    custom_config: {},
    prompt_variables: {}
  }
];

const ModularReportPage: React.FC = () => {
  // 基础状态
  const [formData, setFormData] = useState<ModularReportFormData>({
    enterpriseId: ENTERPRISE_DATA[0].id,
    deviceId: ENTERPRISE_DATA[0].devices[0].id,
    target_date: new Date().toISOString().split('T')[0],
    module_configs: DEFAULT_MODULE_CONFIGS
  });

  // 连接和生成状态
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>('idle');
  
  // 模块进度跟踪
  const [moduleProgress, setModuleProgress] = useState<ModuleProgress[]>([]);
  const [currentModuleIndex, setCurrentModuleIndex] = useState<number>(-1);
  
  // 报告内容
  const [reportContent, setReportContent] = useState<string>('');
  const [chartConfigs, setChartConfigs] = useState<Record<number, any>>({});
  
  // UI状态
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);
  const [selectedModuleIndex, setSelectedModuleIndex] = useState<number>(0);
  
  // SSE连接
  const eventSourceRef = useRef<EventSource | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取当前选择的企业和设备
  const selectedEnterprise = ENTERPRISE_DATA.find(e => e.id === formData.enterpriseId);
  const selectedDevice = selectedEnterprise?.devices.find(d => d.id === formData.deviceId);

  // 初始化模块进度
  useEffect(() => {
    const progress = formData.module_configs.map((config, index) => ({
      module_index: index,
      module_name: config.module_name,
      status: 'pending' as const
    }));
    setModuleProgress(progress);
  }, [formData.module_configs]);

  // 处理表单数据变化
  const handleFormDataChange = useCallback((field: keyof ModularReportFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // 处理模块配置变化
  const handleModuleConfigChange = useCallback((index: number, config: ModuleConfig) => {
    setFormData(prev => ({
      ...prev,
      module_configs: prev.module_configs.map((c, i) => i === index ? config : c)
    }));
  }, []);

  // 添加模块
  const handleAddModule = useCallback((config: ModuleConfig) => {
    setFormData(prev => ({
      ...prev,
      module_configs: [...prev.module_configs, config]
    }));
  }, []);

  // 删除模块
  const handleRemoveModule = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      module_configs: prev.module_configs.filter((_, i) => i !== index)
    }));
  }, []);

  // 处理SSE消息
  const handleSSEMessage = useCallback((message: SSEMessage) => {
    console.log('📨 收到SSE消息:', message);

    switch (message.type) {
      case 'report_created':
        console.log('📋 报告创建成功:', message.metadata);
        break;

      case 'module_start':
        const startIndex = message.metadata?.module_index;
        if (typeof startIndex === 'number') {
          setCurrentModuleIndex(startIndex);
          setModuleProgress(prev => prev.map((p, i) => 
            i === startIndex ? { ...p, status: 'running', start_time: message.timestamp } : p
          ));
        }
        break;

      case 'module_complete':
        const completeIndex = message.metadata?.module_index;
        if (typeof completeIndex === 'number') {
          setModuleProgress(prev => prev.map((p, i) => 
            i === completeIndex ? { ...p, status: 'completed', end_time: message.timestamp } : p
          ));
        }
        break;

      case 'module_content':
        if (message.content) {
          setReportContent(prev => prev + message.content);
        }
        break;

      case 'chart_generation':
        if (message.metadata?.chart_config && typeof currentModuleIndex === 'number') {
          setChartConfigs(prev => ({
            ...prev,
            [currentModuleIndex]: message.metadata.chart_config
          }));
        }
        break;

      case 'module_error':
        const errorIndex = message.metadata?.module_index || currentModuleIndex;
        if (typeof errorIndex === 'number') {
          setModuleProgress(prev => prev.map((p, i) => 
            i === errorIndex ? { 
              ...p, 
              status: 'error', 
              error: message.metadata?.error || '未知错误',
              end_time: message.timestamp 
            } : p
          ));
        }
        break;

      case 'complete':
        setGenerationStatus('completed');
        setConnectionStatus('disconnected');
        setCurrentModuleIndex(-1);
        break;

      case 'error':
        setGenerationStatus('error');
        setConnectionStatus('error');
        console.error('❌ 生成错误:', message.metadata?.error);
        break;

      default:
        if (message.content) {
          setReportContent(prev => prev + message.content);
        }
    }
  }, [currentModuleIndex]);

  // 开始生成报告
  const startGeneration = useCallback(async () => {
    if (generationStatus === 'generating') return;

    try {
      setGenerationStatus('generating');
      setConnectionStatus('connecting');
      setReportContent('');
      setChartConfigs({});
      setCurrentModuleIndex(-1);

      // 重置模块进度
      const resetProgress = formData.module_configs.map((config, index) => ({
        module_index: index,
        module_name: config.module_name,
        status: 'pending' as const
      }));
      setModuleProgress(resetProgress);

      // 构建请求URL
      const url = `/api/device-reports/${formData.enterpriseId}/${formData.deviceId}/modular`;

      // 创建AbortController
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      // 创建EventSource
      const eventSource = new EventSource(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      eventSourceRef.current = eventSource;
      setConnectionStatus('connected');

      eventSource.onmessage = (event) => {
        try {
          const message: SSEMessage = JSON.parse(event.data);
          handleSSEMessage(message);
        } catch (error) {
          console.error('解析SSE消息失败:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error);
        setConnectionStatus('error');
        setGenerationStatus('error');
        eventSource.close();
      };

      eventSource.onopen = () => {
        console.log('SSE连接已建立');
        setConnectionStatus('connected');
      };

    } catch (error) {
      console.error('启动报告生成失败:', error);
      setGenerationStatus('error');
      setConnectionStatus('error');
    }
  }, [formData, generationStatus, handleSSEMessage]);

  // 停止生成
  const stopGeneration = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setGenerationStatus('idle');
    setConnectionStatus('disconnected');
    setCurrentModuleIndex(-1);
  }, []);

  // 清理函数
  useEffect(() => {
    return () => {
      stopGeneration();
    };
  }, [stopGeneration]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Layers className="h-8 w-8 text-blue-600" />
                模块化设备报告生成
              </h1>
              <p className="mt-2 text-gray-600">
                支持5个独立模块的分步执行，包含图表展示和交互功能
              </p>
            </div>

            {/* 连接状态指示器 */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-500' :
                  connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
                  connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-300'
                }`} />
                <span className="text-sm text-gray-600">
                  {connectionStatus === 'connected' && '已连接'}
                  {connectionStatus === 'connecting' && '连接中...'}
                  {connectionStatus === 'error' && '连接错误'}
                  {connectionStatus === 'disconnected' && '未连接'}
                </span>
              </div>

              {/* 生成控制按钮 */}
              {generationStatus === 'generating' ? (
                <button
                  onClick={stopGeneration}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  <Square className="h-4 w-4" />
                  停止生成
                </button>
              ) : (
                <button
                  onClick={startGeneration}
                  disabled={formData.module_configs.length === 0}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Play className="h-4 w-4" />
                  开始生成
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧配置面板 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  报告配置
                </h3>
              </div>
              <div className="p-6 space-y-6">
                {/* 基础配置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    企业选择
                  </label>
                  <select
                    value={formData.enterpriseId}
                    onChange={(e) => {
                      const newEnterpriseId = e.target.value;
                      const newEnterprise = ENTERPRISE_DATA.find(ent => ent.id === newEnterpriseId);
                      handleFormDataChange('enterpriseId', newEnterpriseId);
                      if (newEnterprise && newEnterprise.devices.length > 0) {
                        handleFormDataChange('deviceId', newEnterprise.devices[0].id);
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {ENTERPRISE_DATA.map(enterprise => (
                      <option key={enterprise.id} value={enterprise.id}>
                        {enterprise.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    设备选择
                  </label>
                  <select
                    value={formData.deviceId}
                    onChange={(e) => handleFormDataChange('deviceId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {selectedEnterprise?.devices.map(device => (
                      <option key={device.id} value={device.id}>
                        {device.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    目标日期
                  </label>
                  <input
                    type="date"
                    value={formData.target_date}
                    onChange={(e) => handleFormDataChange('target_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 模块配置按钮 */}
                <div className="pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setIsConfigPanelOpen(true)}
                    className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    <Settings className="h-4 w-4" />
                    配置模块
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 中间进度跟踪面板 */}
          <div className="lg:col-span-1">
            <ModuleProgressTracker
              modules={moduleProgress}
              currentModuleIndex={currentModuleIndex}
              onModuleClick={setSelectedModuleIndex}
            />
          </div>

          {/* 右侧报告展示 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm h-full">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    模块化报告内容
                  </span>
                  {generationStatus === 'generating' && (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                  )}
                </h3>
              </div>
              <div className="p-6">
                <div className="min-h-[800px] max-h-[1200px] overflow-y-auto relative">
                  {reportContent ? (
                    <EnhancedMarkdownRenderer 
                      content={reportContent}
                      className="animate-fade-in"
                      showRawData={false}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-64 text-gray-500">
                      <div className="text-center">
                        <Layers className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">暂无报告内容</p>
                        <p className="text-sm">配置模块并开始生成</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 模块配置面板 */}
      <ModuleConfigPanel
        isOpen={isConfigPanelOpen}
        onClose={() => setIsConfigPanelOpen(false)}
        moduleConfigs={formData.module_configs}
        onModuleConfigChange={handleModuleConfigChange}
        onAddModule={handleAddModule}
        onRemoveModule={handleRemoveModule}
        onStartGeneration={() => {
          setIsConfigPanelOpen(false);
          startGeneration();
        }}
        generationStatus={generationStatus}
      />

      {/* 图表展示区域 */}
      {Object.keys(chartConfigs).length > 0 && (
        <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-white rounded-lg shadow-lg border border-gray-200 z-40">
          <div className="p-4 border-b border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              实时图表
            </h4>
          </div>
          <div className="p-4 max-h-80 overflow-y-auto">
            {Object.entries(chartConfigs).map(([moduleIndex, chartConfig]) => (
              <div key={moduleIndex} className="mb-4 last:mb-0">
                <ChartDisplayPanel
                  chartConfig={chartConfig}
                  onDataPointClick={(dataPoint, sessionData) => {
                    console.log('图表数据点点击:', dataPoint, sessionData);
                    // TODO: 实现会话详情查看
                  }}
                  className="h-48"
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ModularReportPage;
