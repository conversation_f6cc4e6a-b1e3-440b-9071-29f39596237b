import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ModularReportPage from '../pages/ModularReportPage';

// Mock EventSource
class MockEventSource {
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onopen: ((event: Event) => void) | null = null;
  readyState: number = 0;
  url: string;

  constructor(url: string) {
    this.url = url;
    this.readyState = 1; // CONNECTING
    
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = 1; // OPEN
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 100);
  }

  close() {
    this.readyState = 2; // CLOSED
  }

  // Helper method to simulate receiving messages
  simulateMessage(data: any) {
    if (this.onmessage) {
      const event = new MessageEvent('message', {
        data: JSON.stringify(data)
      });
      this.onmessage(event);
    }
  }

  // Helper method to simulate errors
  simulateError() {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

// Mock global EventSource
global.EventSource = MockEventSource as any;

describe('ModularReportPage', () => {
  let mockEventSource: MockEventSource;

  beforeEach(() => {
    // Reset any mocks
    vi.clearAllMocks();
    
    // Mock EventSource constructor to capture instance
    const originalEventSource = global.EventSource;
    global.EventSource = vi.fn().mockImplementation((url: string) => {
      mockEventSource = new MockEventSource(url);
      return mockEventSource;
    });
  });

  afterEach(() => {
    // Clean up
    if (mockEventSource) {
      mockEventSource.close();
    }
  });

  it('renders the page title and description', () => {
    render(<ModularReportPage />);
    
    expect(screen.getByText('模块化设备报告生成')).toBeInTheDocument();
    expect(screen.getByText('支持5个独立模块的分步执行，包含图表展示和交互功能')).toBeInTheDocument();
  });

  it('shows connection status indicator', () => {
    render(<ModularReportPage />);
    
    expect(screen.getByText('未连接')).toBeInTheDocument();
  });

  it('displays start generation button when not generating', () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    expect(startButton).toBeInTheDocument();
    expect(startButton).toBeDisabled(); // Should be disabled when no modules configured
  });

  it('opens module configuration panel when clicking configure button', () => {
    render(<ModularReportPage />);
    
    const configButton = screen.getByText('配置模块');
    fireEvent.click(configButton);
    
    // The ModuleConfigPanel should be rendered (assuming it has a test id or specific text)
    // This would depend on the actual implementation of ModuleConfigPanel
  });

  it('handles SSE connection and message processing', async () => {
    render(<ModularReportPage />);
    
    // Add a module first
    const configButton = screen.getByText('配置模块');
    fireEvent.click(configButton);
    
    // Simulate adding a module (this would depend on ModuleConfigPanel implementation)
    // For now, we'll test the SSE handling directly
    
    const startButton = screen.getByText('开始生成');
    
    // Mock that we have modules configured
    // This would require updating the component state somehow
    
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('连接中...')).toBeInTheDocument();
    });

    // Simulate successful connection
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate receiving a module start message
    mockEventSource.simulateMessage({
      type: 'module_start',
      module_index: 0,
      module_name: '基础使用数据',
      timestamp: new Date().toISOString()
    });

    await waitFor(() => {
      expect(screen.getByText('已连接')).toBeInTheDocument();
    });
  });

  it('handles SSE error correctly', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate connection error
    mockEventSource.simulateError();
    
    await waitFor(() => {
      expect(screen.getByText('连接错误')).toBeInTheDocument();
    });
  });

  it('displays stop button when generating', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('停止生成')).toBeInTheDocument();
    });
  });

  it('stops generation when stop button is clicked', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('停止生成')).toBeInTheDocument();
    });

    const stopButton = screen.getByText('停止生成');
    fireEvent.click(stopButton);
    
    await waitFor(() => {
      expect(screen.getByText('开始生成')).toBeInTheDocument();
      expect(screen.getByText('未连接')).toBeInTheDocument();
    });
  });

  it('processes module progress messages correctly', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate module start
    mockEventSource.simulateMessage({
      type: 'module_start',
      module_index: 0,
      module_name: '基础使用数据',
      timestamp: new Date().toISOString()
    });

    // Simulate module progress
    mockEventSource.simulateMessage({
      type: 'module_progress',
      module_index: 0,
      progress: 50,
      message: '正在处理数据...',
      timestamp: new Date().toISOString()
    });

    // Simulate module completion
    mockEventSource.simulateMessage({
      type: 'module_complete',
      module_index: 0,
      content: '基础使用数据分析完成',
      execution_time: 2.5,
      timestamp: new Date().toISOString()
    });

    // The component should update its state accordingly
    // Specific assertions would depend on how the progress is displayed
  });

  it('handles chart configuration messages', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate chart configuration message
    mockEventSource.simulateMessage({
      type: 'chart_config',
      module_index: 0,
      chart_config: {
        type: 'line',
        title: '会话趋势',
        data: {
          labels: ['09:00', '10:00', '11:00'],
          datasets: [{
            label: '会话数量',
            data: [10, 15, 12]
          }]
        }
      },
      timestamp: new Date().toISOString()
    });

    // The chart should be displayed
    // This would require checking if ChartDisplayPanel is rendered with the correct config
  });

  it('handles report completion', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate report completion
    mockEventSource.simulateMessage({
      type: 'complete',
      report_id: 'test-report-123',
      total_execution_time: 15.2,
      total_tokens_used: 1500,
      timestamp: new Date().toISOString()
    });

    await waitFor(() => {
      expect(screen.getByText('开始生成')).toBeInTheDocument();
      expect(screen.getByText('未连接')).toBeInTheDocument();
    });
  });

  it('handles error messages', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate error message
    mockEventSource.simulateMessage({
      type: 'error',
      error: '数据获取失败',
      module_index: 0,
      timestamp: new Date().toISOString()
    });

    // Error should be displayed
    // This would depend on how errors are shown in the UI
  });

  it('cleans up EventSource on unmount', () => {
    const { unmount } = render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    const closeSpy = vi.spyOn(mockEventSource, 'close');
    
    unmount();
    
    expect(closeSpy).toHaveBeenCalled();
  });

  it('validates form data before starting generation', () => {
    render(<ModularReportPage />);
    
    // Try to start generation without configuring modules
    const startButton = screen.getByText('开始生成');
    expect(startButton).toBeDisabled();
    
    // The button should remain disabled until modules are configured
  });

  it('displays real-time report content', async () => {
    render(<ModularReportPage />);
    
    const startButton = screen.getByText('开始生成');
    fireEvent.click(startButton);
    
    await waitFor(() => {
      expect(mockEventSource).toBeDefined();
    });

    // Simulate content message
    mockEventSource.simulateMessage({
      type: 'content',
      content: '## 基础使用数据\n\n今日会话总数: 150',
      module_index: 0,
      timestamp: new Date().toISOString()
    });

    // Content should be displayed in the report area
    // This would depend on how content is rendered
  });
});
