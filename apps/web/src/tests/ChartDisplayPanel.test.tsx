import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ChartDisplayPanel from '../components/ChartDisplayPanel';

// Mock ECharts
const mockEChartsInstance = {
  setOption: vi.fn(),
  resize: vi.fn(),
  dispose: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  getDataURL: vi.fn(() => 'data:image/png;base64,mock-image-data'),
  clear: vi.fn()
};

const mockECharts = {
  init: vi.fn(() => mockEChartsInstance),
  dispose: vi.fn(),
  registerTheme: vi.fn()
};

// Mock echarts module
vi.mock('echarts', () => ({
  default: mockECharts,
  init: mockECharts.init,
  dispose: mockECharts.dispose,
  registerTheme: mockECharts.registerTheme
}));

describe('ChartDisplayPanel', () => {
  const mockChartConfig = {
    type: 'line',
    title: '测试图表',
    data: {
      labels: ['09:00', '10:00', '11:00', '12:00'],
      datasets: [{
        label: '会话数量',
        data: [10, 15, 12, 18],
        backgroundColor: '#3b82f6',
        borderColor: '#3b82f6'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    },
    clickable: true,
    sessionData: [
      { sessionIds: ['session1', 'session2'], metadata: {} },
      { sessionIds: ['session3'], metadata: {} },
      { sessionIds: ['session4', 'session5'], metadata: {} },
      { sessionIds: ['session6'], metadata: {} }
    ]
  };

  const mockOnDataPointClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders chart container', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const chartContainer = screen.getByTestId('chart-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('initializes ECharts instance on mount', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    expect(mockECharts.init).toHaveBeenCalled();
    expect(mockEChartsInstance.setOption).toHaveBeenCalled();
  });

  it('converts chart config to ECharts format correctly', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const setOptionCall = mockEChartsInstance.setOption.mock.calls[0][0];
    
    expect(setOptionCall.title.text).toBe('测试图表');
    expect(setOptionCall.xAxis.data).toEqual(['09:00', '10:00', '11:00', '12:00']);
    expect(setOptionCall.series[0].data).toEqual([10, 15, 12, 18]);
    expect(setOptionCall.series[0].type).toBe('line');
  });

  it('handles different chart types correctly', () => {
    const barChartConfig = {
      ...mockChartConfig,
      type: 'bar'
    };

    render(
      <ChartDisplayPanel 
        chartConfig={barChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const setOptionCall = mockEChartsInstance.setOption.mock.calls[0][0];
    expect(setOptionCall.series[0].type).toBe('bar');
  });

  it('handles pie chart configuration', () => {
    const pieChartConfig = {
      type: 'pie',
      title: '饼图测试',
      data: {
        labels: ['类型A', '类型B', '类型C'],
        datasets: [{
          data: [30, 40, 30],
          backgroundColor: ['#ff6384', '#36a2eb', '#ffce56']
        }]
      },
      clickable: true,
      sessionData: []
    };

    render(
      <ChartDisplayPanel 
        chartConfig={pieChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const setOptionCall = mockEChartsInstance.setOption.mock.calls[0][0];
    expect(setOptionCall.series[0].type).toBe('pie');
    expect(setOptionCall.series[0].data).toHaveLength(3);
    expect(setOptionCall.series[0].data[0].name).toBe('类型A');
    expect(setOptionCall.series[0].data[0].value).toBe(30);
  });

  it('sets up click event listener for clickable charts', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    expect(mockEChartsInstance.on).toHaveBeenCalledWith('click', expect.any(Function));
  });

  it('does not set up click event for non-clickable charts', () => {
    const nonClickableConfig = {
      ...mockChartConfig,
      clickable: false
    };

    render(
      <ChartDisplayPanel 
        chartConfig={nonClickableConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    expect(mockEChartsInstance.on).not.toHaveBeenCalled();
  });

  it('handles chart click events correctly', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    // Get the click handler that was registered
    const clickHandler = mockEChartsInstance.on.mock.calls.find(
      call => call[0] === 'click'
    )?.[1];

    expect(clickHandler).toBeDefined();

    // Simulate a click event
    const mockClickEvent = {
      dataIndex: 1,
      name: '10:00',
      value: 15
    };

    clickHandler(mockClickEvent);

    expect(mockOnDataPointClick).toHaveBeenCalledWith(
      mockClickEvent,
      mockChartConfig.sessionData[1]
    );
  });

  it('shows fullscreen button', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const fullscreenButton = screen.getByTitle('全屏显示');
    expect(fullscreenButton).toBeInTheDocument();
  });

  it('shows export button', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const exportButton = screen.getByTitle('导出图片');
    expect(exportButton).toBeInTheDocument();
  });

  it('handles export functionality', async () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const exportButton = screen.getByTitle('导出图片');
    
    // Mock URL.createObjectURL and document.createElement
    const mockCreateObjectURL = vi.fn(() => 'blob:mock-url');
    const mockRevokeObjectURL = vi.fn();
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;

    const mockLink = {
      href: '',
      download: '',
      click: vi.fn()
    };
    const mockCreateElement = vi.fn(() => mockLink);
    document.createElement = mockCreateElement;

    fireEvent.click(exportButton);

    await waitFor(() => {
      expect(mockEChartsInstance.getDataURL).toHaveBeenCalled();
      expect(mockCreateElement).toHaveBeenCalledWith('a');
      expect(mockLink.click).toHaveBeenCalled();
    });
  });

  it('handles fullscreen toggle', () => {
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const fullscreenButton = screen.getByTitle('全屏显示');
    fireEvent.click(fullscreenButton);

    // Check if fullscreen class is applied
    const chartContainer = screen.getByTestId('chart-container');
    expect(chartContainer.parentElement).toHaveClass('fixed', 'inset-0', 'z-50');
  });

  it('resizes chart when container size changes', () => {
    const { rerender } = render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
        className="w-full h-64"
      />
    );

    // Simulate window resize
    global.dispatchEvent(new Event('resize'));

    expect(mockEChartsInstance.resize).toHaveBeenCalled();
  });

  it('disposes chart instance on unmount', () => {
    const { unmount } = render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    unmount();

    expect(mockEChartsInstance.dispose).toHaveBeenCalled();
  });

  it('updates chart when config changes', () => {
    const { rerender } = render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    const updatedConfig = {
      ...mockChartConfig,
      title: '更新后的图表'
    };

    rerender(
      <ChartDisplayPanel 
        chartConfig={updatedConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    // setOption should be called again with updated config
    expect(mockEChartsInstance.setOption).toHaveBeenCalledTimes(2);
  });

  it('handles loading state', () => {
    const loadingConfig = {
      ...mockChartConfig,
      loading: true
    };

    render(
      <ChartDisplayPanel 
        chartConfig={loadingConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    expect(screen.getByText('图表加载中...')).toBeInTheDocument();
  });

  it('handles error state', () => {
    const errorConfig = {
      ...mockChartConfig,
      error: '图表加载失败'
    };

    render(
      <ChartDisplayPanel 
        chartConfig={errorConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    );

    expect(screen.getByText('图表加载失败')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-chart-class';
    
    render(
      <ChartDisplayPanel 
        chartConfig={mockChartConfig}
        onDataPointClick={mockOnDataPointClick}
        className={customClass}
      />
    );

    const chartContainer = screen.getByTestId('chart-container');
    expect(chartContainer.parentElement).toHaveClass(customClass);
  });
});
