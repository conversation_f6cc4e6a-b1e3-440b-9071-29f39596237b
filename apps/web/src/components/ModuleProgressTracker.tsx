import React from 'react';
import { 
  Check<PERSON>ircle2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2, <PERSON>, 
  <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Star, Lightbulb 
} from 'lucide-react';

interface ModuleProgress {
  module_index: number;
  module_name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  start_time?: string;
  end_time?: string;
  error?: string;
  content?: string;
  chart_config?: any;
}

interface ModuleProgressTrackerProps {
  modules: ModuleProgress[];
  currentModuleIndex: number;
  onModuleClick?: (index: number) => void;
}

// 模块类型图标映射
const MODULE_ICONS = {
  'basic_usage': BarChart3,
  'event_analysis': AlertTriangle,
  'service_quality': Star,
  'insight_analysis': TrendingUp,
  'business_advice': Lightbulb
};

// 状态图标映射
const STATUS_ICONS = {
  pending: Clock,
  running: Loader2,
  completed: CheckCircle2,
  error: XCircle
};

// 状态颜色映射
const STATUS_COLORS = {
  pending: 'text-gray-400',
  running: 'text-blue-500',
  completed: 'text-green-500',
  error: 'text-red-500'
};

// 状态背景色映射
const STATUS_BG_COLORS = {
  pending: 'bg-gray-50 border-gray-200',
  running: 'bg-blue-50 border-blue-200',
  completed: 'bg-green-50 border-green-200',
  error: 'bg-red-50 border-red-200'
};

const ModuleProgressTracker: React.FC<ModuleProgressTrackerProps> = ({
  modules,
  currentModuleIndex,
  onModuleClick
}) => {
  // 计算总体进度
  const completedCount = modules.filter(m => m.status === 'completed').length;
  const totalCount = modules.length;
  const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

  // 格式化时间
  const formatTime = (timeString?: string) => {
    if (!timeString) return '';
    return new Date(timeString).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 计算执行时长
  const calculateDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '';
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}秒`;
    } else {
      const minutes = Math.floor(duration / 60);
      const seconds = duration % 60;
      return `${minutes}分${seconds}秒`;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Play className="h-5 w-5" />
          执行进度
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          {completedCount}/{totalCount} 个模块已完成
        </p>
        
        {/* 总体进度条 */}
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>总体进度</span>
            <span>{Math.round(progressPercentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          {modules.map((module, index) => {
            const StatusIcon = STATUS_ICONS[module.status];
            const isActive = index === currentModuleIndex;
            const isClickable = onModuleClick && (module.status === 'completed' || module.status === 'error');
            
            // 尝试从模块名称推断模块类型
            let moduleType = 'basic_usage';
            if (module.module_name.includes('事件') || module.module_name.includes('分析')) {
              moduleType = 'event_analysis';
            } else if (module.module_name.includes('服务') || module.module_name.includes('质量')) {
              moduleType = 'service_quality';
            } else if (module.module_name.includes('洞察') || module.module_name.includes('观察')) {
              moduleType = 'insight_analysis';
            } else if (module.module_name.includes('建议') || module.module_name.includes('经营')) {
              moduleType = 'business_advice';
            }
            
            const ModuleIcon = MODULE_ICONS[moduleType as keyof typeof MODULE_ICONS] || BarChart3;

            return (
              <div
                key={index}
                className={`
                  relative p-4 rounded-lg border-2 transition-all duration-200
                  ${STATUS_BG_COLORS[module.status]}
                  ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
                  ${isClickable ? 'cursor-pointer hover:shadow-md' : ''}
                `}
                onClick={() => isClickable && onModuleClick?.(index)}
              >
                {/* 模块序号指示器 */}
                <div className="absolute -left-3 -top-3 w-6 h-6 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center text-xs font-semibold">
                  {index + 1}
                </div>

                <div className="flex items-start gap-3">
                  {/* 模块图标 */}
                  <div className={`flex-shrink-0 p-2 rounded-lg ${
                    module.status === 'completed' ? 'bg-green-100' :
                    module.status === 'running' ? 'bg-blue-100' :
                    module.status === 'error' ? 'bg-red-100' : 'bg-gray-100'
                  }`}>
                    <ModuleIcon className={`h-5 w-5 ${
                      module.status === 'completed' ? 'text-green-600' :
                      module.status === 'running' ? 'text-blue-600' :
                      module.status === 'error' ? 'text-red-600' : 'text-gray-600'
                    }`} />
                  </div>

                  <div className="flex-1 min-w-0">
                    {/* 模块名称和状态 */}
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {module.module_name}
                      </h4>
                      <div className="flex items-center gap-1">
                        <StatusIcon className={`h-4 w-4 ${STATUS_COLORS[module.status]} ${
                          module.status === 'running' ? 'animate-spin' : ''
                        }`} />
                      </div>
                    </div>

                    {/* 状态文本 */}
                    <div className="mt-1">
                      <span className={`text-xs font-medium ${STATUS_COLORS[module.status]}`}>
                        {module.status === 'pending' && '等待执行'}
                        {module.status === 'running' && '正在执行...'}
                        {module.status === 'completed' && '执行完成'}
                        {module.status === 'error' && '执行失败'}
                      </span>
                    </div>

                    {/* 时间信息 */}
                    {(module.start_time || module.end_time) && (
                      <div className="mt-2 text-xs text-gray-500 space-y-1">
                        {module.start_time && (
                          <div>开始: {formatTime(module.start_time)}</div>
                        )}
                        {module.end_time && (
                          <div>结束: {formatTime(module.end_time)}</div>
                        )}
                        {module.start_time && (
                          <div className="font-medium">
                            耗时: {calculateDuration(module.start_time, module.end_time)}
                          </div>
                        )}
                      </div>
                    )}

                    {/* 错误信息 */}
                    {module.status === 'error' && module.error && (
                      <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
                        {module.error}
                      </div>
                    )}

                    {/* 完成状态的额外信息 */}
                    {module.status === 'completed' && (
                      <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                        {module.chart_config && (
                          <span className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3" />
                            包含图表
                          </span>
                        )}
                        {isClickable && (
                          <span className="text-blue-600">点击查看详情</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* 进度条（仅在运行时显示） */}
                {module.status === 'running' && (
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* 空状态 */}
        {modules.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">暂无模块配置</p>
            <p className="text-sm">请先配置报告模块</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModuleProgressTracker;
