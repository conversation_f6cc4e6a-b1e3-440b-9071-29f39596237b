import React, { useState, useEffect } from 'react';
import { 
  X, MessageCir<PERSON>, Clock, User, Bot, ThumbsUp, ThumbsDown, 
  AlertTriangle, CheckCircle, Star, Calendar, Hash, ExternalLink
} from 'lucide-react';

interface SessionMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: any;
}

interface SessionDetail {
  session_id: string;
  enterprise_id: string;
  device_id: string;
  start_time: string;
  end_time?: string;
  total_messages: number;
  user_satisfaction?: number;
  session_type?: string;
  tags?: string[];
  summary?: string;
  messages: SessionMessage[];
  statistics?: {
    duration: number;
    response_time_avg: number;
    user_rating?: number;
    resolution_status?: string;
  };
}

interface SessionDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId?: string;
  sessionData?: SessionDetail;
  onLoadSession?: (sessionId: string) => Promise<SessionDetail>;
}

const SessionDetailModal: React.FC<SessionDetailModalProps> = ({
  isOpen,
  onClose,
  sessionId,
  sessionData,
  onLoadSession
}) => {
  const [session, setSession] = useState<SessionDetail | null>(sessionData || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);

  // 加载会话详情
  useEffect(() => {
    if (isOpen && sessionId && !sessionData && onLoadSession) {
      loadSessionDetail();
    }
  }, [isOpen, sessionId, sessionData, onLoadSession]);

  const loadSessionDetail = async () => {
    if (!sessionId || !onLoadSession) return;

    try {
      setLoading(true);
      setError(null);
      const data = await onLoadSession(sessionId);
      setSession(data);
    } catch (err) {
      console.error('加载会话详情失败:', err);
      setError('加载会话详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 格式化持续时间
  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分`;
    }
  };

  // 获取满意度颜色
  const getSatisfactionColor = (satisfaction?: number) => {
    if (!satisfaction) return 'text-gray-400';
    if (satisfaction >= 4) return 'text-green-600';
    if (satisfaction >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取满意度图标
  const getSatisfactionIcon = (satisfaction?: number) => {
    if (!satisfaction) return null;
    if (satisfaction >= 4) return <ThumbsUp className="h-4 w-4" />;
    if (satisfaction >= 3) return <Star className="h-4 w-4" />;
    return <ThumbsDown className="h-4 w-4" />;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <MessageCircle className="h-6 w-6" />
                会话详情
              </h2>
              {session && (
                <p className="text-sm text-gray-600 mt-1">
                  会话ID: {session.session_id}
                </p>
              )}
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-hidden">
            {loading && (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">加载会话详情中...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-red-600">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">{error}</p>
                  <button
                    onClick={loadSessionDetail}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    重试
                  </button>
                </div>
              </div>
            )}

            {session && !loading && !error && (
              <div className="h-full flex">
                {/* 左侧会话信息 */}
                <div className="w-1/3 border-r border-gray-200 p-6 overflow-y-auto">
                  <div className="space-y-6">
                    {/* 基础信息 */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">基础信息</h3>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">开始时间:</span>
                          <span className="text-sm font-medium">{formatTime(session.start_time)}</span>
                        </div>
                        
                        {session.end_time && (
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-600">结束时间:</span>
                            <span className="text-sm font-medium">{formatTime(session.end_time)}</span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2">
                          <Hash className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">消息数量:</span>
                          <span className="text-sm font-medium">{session.total_messages}</span>
                        </div>
                        
                        {session.user_satisfaction && (
                          <div className="flex items-center gap-2">
                            {getSatisfactionIcon(session.user_satisfaction)}
                            <span className="text-sm text-gray-600">用户满意度:</span>
                            <span className={`text-sm font-medium ${getSatisfactionColor(session.user_satisfaction)}`}>
                              {session.user_satisfaction}/5
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 统计信息 */}
                    {session.statistics && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">统计信息</h3>
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-600">会话时长:</span>
                            <span className="text-sm font-medium">
                              {formatDuration(session.statistics.duration)}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Bot className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-600">平均响应时间:</span>
                            <span className="text-sm font-medium">
                              {session.statistics.response_time_avg.toFixed(2)}秒
                            </span>
                          </div>
                          
                          {session.statistics.resolution_status && (
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-600">解决状态:</span>
                              <span className="text-sm font-medium">
                                {session.statistics.resolution_status}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* 标签 */}
                    {session.tags && session.tags.length > 0 && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">标签</h3>
                        <div className="flex flex-wrap gap-2">
                          {session.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 摘要 */}
                    {session.summary && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">会话摘要</h3>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {session.summary}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* 右侧消息列表 */}
                <div className="flex-1 flex flex-col">
                  <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">对话记录</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      共 {session.messages.length} 条消息
                    </p>
                  </div>
                  
                  <div className="flex-1 overflow-y-auto p-6">
                    <div className="space-y-4">
                      {session.messages.map((message, index) => (
                        <div
                          key={message.id}
                          className={`flex gap-3 ${
                            message.role === 'user' ? 'justify-end' : 'justify-start'
                          }`}
                        >
                          <div
                            className={`max-w-[70%] rounded-lg p-4 ${
                              message.role === 'user'
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 text-gray-900'
                            } ${
                              selectedMessageId === message.id
                                ? 'ring-2 ring-blue-500 ring-opacity-50'
                                : ''
                            }`}
                            onClick={() => setSelectedMessageId(
                              selectedMessageId === message.id ? null : message.id
                            )}
                          >
                            <div className="flex items-center gap-2 mb-2">
                              {message.role === 'user' ? (
                                <User className="h-4 w-4" />
                              ) : (
                                <Bot className="h-4 w-4" />
                              )}
                              <span className="text-xs opacity-75">
                                {message.role === 'user' ? '用户' : 'AI助手'}
                              </span>
                              <span className="text-xs opacity-75">
                                {formatTime(message.timestamp)}
                              </span>
                            </div>
                            
                            <div className="text-sm leading-relaxed whitespace-pre-wrap">
                              {message.content}
                            </div>
                            
                            {message.metadata && selectedMessageId === message.id && (
                              <div className="mt-3 pt-3 border-t border-gray-300 border-opacity-30">
                                <div className="text-xs opacity-75">
                                  <pre className="whitespace-pre-wrap">
                                    {JSON.stringify(message.metadata, null, 2)}
                                  </pre>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 底部操作区域 */}
          {session && (
            <div className="p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  点击消息可查看详细信息
                </div>
                
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => {
                      // TODO: 实现导出功能
                      console.log('导出会话:', session.session_id);
                    }}
                    className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    导出会话
                  </button>
                  
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionDetailModal;
