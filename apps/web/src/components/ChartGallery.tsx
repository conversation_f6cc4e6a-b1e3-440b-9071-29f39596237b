import React, { useState, useEffect } from 'react';
import { 
  BarChart3, Grid, List, Search, Filter, Download, 
  Maximize2, Minimize2, RefreshCw, Plus, Settings
} from 'lucide-react';
import EnhancedChartComponent from './EnhancedChartComponent';

interface ChartGalleryItem {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area' | 'heatmap';
  data: any[];
  interactive: boolean;
  category?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

interface ChartGalleryProps {
  charts: ChartGalleryItem[];
  onChartClick?: (chart: ChartGalleryItem) => void;
  onLoadSessionDetail?: (sessionId: string) => Promise<any>;
  onCreateChart?: () => void;
  onExportAll?: () => void;
  className?: string;
}

type ViewMode = 'grid' | 'list';
type SortBy = 'title' | 'type' | 'createdAt' | 'updatedAt';

const ChartGallery: React.FC<ChartGalleryProps> = ({
  charts,
  onChartClick,
  onLoadSessionDetail,
  onCreateChart,
  onExportAll,
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [sortBy, setSortBy] = useState<SortBy>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filteredCharts, setFilteredCharts] = useState(charts);
  const [expandedChart, setExpandedChart] = useState<string | null>(null);

  // 获取所有分类
  const categories = Array.from(new Set(charts.map(chart => chart.category).filter(Boolean)));
  
  // 获取所有图表类型
  const chartTypes = Array.from(new Set(charts.map(chart => chart.type)));

  // 应用过滤和排序
  useEffect(() => {
    let filtered = charts;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(chart =>
        chart.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        chart.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // 分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(chart => chart.category === selectedCategory);
    }

    // 类型过滤
    if (selectedType) {
      filtered = filtered.filter(chart => chart.type === selectedType);
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCharts(filtered);
  }, [charts, searchTerm, selectedCategory, selectedType, sortBy, sortOrder]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取图表类型图标
  const getChartTypeIcon = (type: string) => {
    switch (type) {
      case 'bar': return <BarChart3 className="h-4 w-4" />;
      case 'line': return <BarChart3 className="h-4 w-4" />;
      case 'pie': return <BarChart3 className="h-4 w-4" />;
      default: return <BarChart3 className="h-4 w-4" />;
    }
  };

  // 获取图表类型颜色
  const getChartTypeColor = (type: string) => {
    switch (type) {
      case 'bar': return 'text-blue-600 bg-blue-100';
      case 'line': return 'text-green-600 bg-green-100';
      case 'pie': return 'text-purple-600 bg-purple-100';
      case 'scatter': return 'text-orange-600 bg-orange-100';
      case 'area': return 'text-teal-600 bg-teal-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* 头部控制栏 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              图表集合
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              共 {filteredCharts.length} 个图表
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {onCreateChart && (
              <button
                onClick={onCreateChart}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                新建图表
              </button>
            )}
            
            {onExportAll && (
              <button
                onClick={onExportAll}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                <Download className="h-4 w-4" />
                导出全部
              </button>
            )}
          </div>
        </div>

        {/* 搜索和过滤器 */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索图表标题或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有分类</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有类型</option>
            {chartTypes.map(type => (
              <option key={type} value={type}>{type.toUpperCase()}</option>
            ))}
          </select>
        </div>

        {/* 视图控制和排序 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'grid' 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'list' 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">排序:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortBy)}
              className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="title">标题</option>
              <option value="type">类型</option>
              <option value="createdAt">创建时间</option>
              <option value="updatedAt">更新时间</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-1 text-gray-600 hover:text-gray-900 transition-colors"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>
      </div>

      {/* 图表内容区域 */}
      <div className="p-6">
        {filteredCharts.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <BarChart3 className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">暂无图表</p>
            <p className="text-sm">尝试调整搜索条件或创建新图表</p>
          </div>
        ) : (
          <div className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }>
            {filteredCharts.map(chart => (
              <div
                key={chart.id}
                className={`border border-gray-200 rounded-lg hover:shadow-md transition-shadow ${
                  viewMode === 'list' ? 'p-4' : ''
                }`}
              >
                {viewMode === 'grid' ? (
                  <div>
                    {/* 网格视图 */}
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900 truncate">
                          {chart.title}
                        </h3>
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${getChartTypeColor(chart.type)}`}>
                            {chart.type.toUpperCase()}
                          </span>
                          <button
                            onClick={() => setExpandedChart(
                              expandedChart === chart.id ? null : chart.id
                            )}
                            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          >
                            {expandedChart === chart.id ? (
                              <Minimize2 className="h-4 w-4" />
                            ) : (
                              <Maximize2 className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        更新于 {formatDate(chart.updatedAt)}
                      </div>
                      
                      {chart.tags && chart.tags.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {chart.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                          {chart.tags.length > 3 && (
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                              +{chart.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {expandedChart === chart.id && (
                      <div className="p-4">
                        <EnhancedChartComponent
                          chartConfig={{
                            id: chart.id,
                            title: chart.title,
                            type: chart.type,
                            data: chart.data,
                            interactive: chart.interactive
                          }}
                          onLoadSessionDetail={onLoadSessionDetail}
                          height={300}
                          showControls={false}
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    {/* 列表视图 */}
                    <div className="flex items-center gap-4">
                      <div className={`p-2 rounded ${getChartTypeColor(chart.type)}`}>
                        {getChartTypeIcon(chart.type)}
                      </div>
                      
                      <div>
                        <h3 className="font-medium text-gray-900">{chart.title}</h3>
                        <div className="text-sm text-gray-500">
                          {chart.category && `${chart.category} • `}
                          更新于 {formatDate(chart.updatedAt)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getChartTypeColor(chart.type)}`}>
                        {chart.type.toUpperCase()}
                      </span>
                      
                      {chart.interactive && (
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          交互式
                        </span>
                      )}
                      
                      <button
                        onClick={() => onChartClick?.(chart)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <Maximize2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartGallery;
