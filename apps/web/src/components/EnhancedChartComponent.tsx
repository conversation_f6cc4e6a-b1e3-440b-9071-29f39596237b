import React, { useState, useEffect, useRef } from 'react';
import { 
  Bar<PERSON><PERSON>3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Download, 
  Maximize2, Filter, Settings, Refresh<PERSON>w, Eye, EyeOff
} from 'lucide-react';
import ChartDisplayPanel from './ChartDisplayPanel';
import SessionDetailModal from './SessionDetailModal';

interface ChartDataPoint {
  label: string;
  value: number;
  sessionIds?: string[];
  metadata?: any;
  color?: string;
}

interface EnhancedChartConfig {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area' | 'heatmap';
  data: ChartDataPoint[];
  options?: any;
  interactive: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableZoom?: boolean;
  enableBrush?: boolean;
  timeRange?: {
    start: string;
    end: string;
  };
  filters?: {
    [key: string]: any;
  };
}

interface EnhancedChartComponentProps {
  chartConfig: EnhancedChartConfig;
  onDataPointClick?: (dataPoint: ChartDataPoint, sessionIds?: string[]) => void;
  onLoadSessionDetail?: (sessionId: string) => Promise<any>;
  className?: string;
  height?: number;
  showControls?: boolean;
}

const EnhancedChartComponent: React.FC<EnhancedChartComponentProps> = ({
  chartConfig,
  onDataPointClick,
  onLoadSessionDetail,
  className = '',
  height = 400,
  showControls = true
}) => {
  const [isSessionModalOpen, setIsSessionModalOpen] = useState(false);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [chartFilters, setChartFilters] = useState(chartConfig.filters || {});
  const [isLegendVisible, setIsLegendVisible] = useState(chartConfig.showLegend !== false);
  const [isTooltipVisible, setIsTooltipVisible] = useState(chartConfig.showTooltip !== false);
  const [filteredData, setFilteredData] = useState(chartConfig.data);

  // 应用过滤器
  useEffect(() => {
    let filtered = chartConfig.data;
    
    // 应用各种过滤器
    Object.entries(chartFilters).forEach(([filterKey, filterValue]) => {
      if (filterValue !== null && filterValue !== undefined && filterValue !== '') {
        filtered = filtered.filter(item => {
          const itemValue = item.metadata?.[filterKey] || item[filterKey as keyof ChartDataPoint];
          if (typeof filterValue === 'string') {
            return String(itemValue).toLowerCase().includes(filterValue.toLowerCase());
          }
          return itemValue === filterValue;
        });
      }
    });
    
    setFilteredData(filtered);
  }, [chartConfig.data, chartFilters]);

  // 处理数据点点击
  const handleDataPointClick = (dataPoint: any, sessionData?: any[]) => {
    console.log('图表数据点点击:', dataPoint, sessionData);
    
    // 查找对应的原始数据点
    const originalDataPoint = filteredData.find(item => 
      item.label === dataPoint.name || item.value === dataPoint.value
    );
    
    if (originalDataPoint && onDataPointClick) {
      onDataPointClick(originalDataPoint, originalDataPoint.sessionIds);
    }
    
    // 如果有会话ID，打开会话详情
    if (originalDataPoint?.sessionIds && originalDataPoint.sessionIds.length > 0) {
      setSelectedSessionId(originalDataPoint.sessionIds[0]);
      setIsSessionModalOpen(true);
    }
  };

  // 转换数据为ECharts格式
  const convertToEChartsFormat = () => {
    const baseConfig = {
      type: chartConfig.type,
      title: chartConfig.title,
      data: {
        labels: filteredData.map(item => item.label),
        datasets: [{
          label: chartConfig.title,
          data: filteredData.map(item => item.value),
          backgroundColor: filteredData.map(item => item.color || '#3b82f6'),
          borderColor: filteredData.map(item => item.color || '#3b82f6'),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: isLegendVisible
          },
          tooltip: {
            enabled: isTooltipVisible,
            callbacks: {
              afterBody: (context: any) => {
                const dataIndex = context[0].dataIndex;
                const dataPoint = filteredData[dataIndex];
                if (dataPoint.sessionIds && dataPoint.sessionIds.length > 0) {
                  return `关联会话: ${dataPoint.sessionIds.length} 个`;
                }
                return '';
              }
            }
          }
        },
        scales: chartConfig.type !== 'pie' ? {
          y: {
            beginAtZero: true
          }
        } : undefined,
        ...chartConfig.options
      },
      clickable: chartConfig.interactive,
      sessionData: filteredData.map(item => ({
        sessionIds: item.sessionIds || [],
        metadata: item.metadata
      }))
    };

    return baseConfig;
  };

  // 导出图表数据
  const exportChartData = () => {
    const dataToExport = {
      title: chartConfig.title,
      type: chartConfig.type,
      data: filteredData,
      exportTime: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${chartConfig.title}_data.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  // 刷新图表数据
  const refreshChart = () => {
    // 重置过滤器
    setChartFilters({});
    setFilteredData(chartConfig.data);
  };

  // 获取可用的过滤器选项
  const getFilterOptions = (filterKey: string) => {
    const values = new Set();
    chartConfig.data.forEach(item => {
      const value = item.metadata?.[filterKey] || item[filterKey as keyof ChartDataPoint];
      if (value !== null && value !== undefined) {
        values.add(value);
      }
    });
    return Array.from(values);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* 图表控制栏 */}
      {showControls && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                {chartConfig.type === 'line' && <LineChart className="h-5 w-5" />}
                {chartConfig.type === 'bar' && <BarChart3 className="h-5 w-5" />}
                {chartConfig.type === 'pie' && <PieChart className="h-5 w-5" />}
                {(chartConfig.type === 'scatter' || chartConfig.type === 'area') && <TrendingUp className="h-5 w-5" />}
                {chartConfig.title}
              </h3>
              
              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                {filteredData.length} 个数据点
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              {/* 图例控制 */}
              <button
                onClick={() => setIsLegendVisible(!isLegendVisible)}
                className={`p-2 rounded transition-colors ${
                  isLegendVisible 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
                title={isLegendVisible ? '隐藏图例' : '显示图例'}
              >
                {isLegendVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              </button>
              
              {/* 刷新按钮 */}
              <button
                onClick={refreshChart}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="刷新图表"
              >
                <RefreshCw className="h-4 w-4" />
              </button>
              
              {/* 导出按钮 */}
              <button
                onClick={exportChartData}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="导出数据"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          {/* 过滤器区域 */}
          {Object.keys(chartConfig.filters || {}).length > 0 && (
            <div className="mt-4 flex items-center gap-4">
              <Filter className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">过滤器:</span>
              
              {Object.keys(chartConfig.filters || {}).map(filterKey => (
                <div key={filterKey} className="flex items-center gap-2">
                  <label className="text-sm text-gray-600">{filterKey}:</label>
                  <select
                    value={chartFilters[filterKey] || ''}
                    onChange={(e) => setChartFilters(prev => ({
                      ...prev,
                      [filterKey]: e.target.value
                    }))}
                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部</option>
                    {getFilterOptions(filterKey).map(option => (
                      <option key={String(option)} value={String(option)}>
                        {String(option)}
                      </option>
                    ))}
                  </select>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 图表内容 */}
      <div style={{ height: `${height}px` }}>
        <ChartDisplayPanel
          chartConfig={convertToEChartsFormat()}
          onDataPointClick={handleDataPointClick}
        />
      </div>

      {/* 图表统计信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>数据点: {filteredData.length}</span>
            <span>
              总会话: {filteredData.reduce((sum, item) => sum + (item.sessionIds?.length || 0), 0)}
            </span>
            {chartConfig.timeRange && (
              <span>
                时间范围: {new Date(chartConfig.timeRange.start).toLocaleDateString()} - {new Date(chartConfig.timeRange.end).toLocaleDateString()}
              </span>
            )}
          </div>
          
          {chartConfig.interactive && (
            <span className="text-blue-600">
              💡 点击数据点查看详情
            </span>
          )}
        </div>
      </div>

      {/* 会话详情模态框 */}
      <SessionDetailModal
        isOpen={isSessionModalOpen}
        onClose={() => {
          setIsSessionModalOpen(false);
          setSelectedSessionId(null);
        }}
        sessionId={selectedSessionId || undefined}
        onLoadSession={onLoadSessionDetail}
      />
    </div>
  );
};

export default EnhancedChartComponent;
