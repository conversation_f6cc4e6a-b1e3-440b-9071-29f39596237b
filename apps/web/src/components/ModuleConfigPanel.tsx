import React, { useState, useEffect } from 'react';
import { 
  X, Plus, Settings, Database, Brain, BarChart3, 
  AlertTriangle, Star, TrendingUp, Lightbulb, Trash2,
  ChevronDown, ChevronRight, Play, Loader2
} from 'lucide-react';

interface ModuleConfig {
  module_type: string;
  module_name: string;
  module_description: string;
  data_sources: Record<string, any>;
  primary_data_source: string;
  execution_mode: string;
  use_llm: boolean;
  template_name?: string;
  custom_config: Record<string, any>;
  system_prompt?: string;
  user_prompt?: string;
  prompt_variables: Record<string, any>;
}

interface ModuleConfigPanelProps {
  isOpen: boolean;
  onClose: () => void;
  moduleConfigs: ModuleConfig[];
  onModuleConfigChange: (index: number, config: ModuleConfig) => void;
  onAddModule: (config: ModuleConfig) => void;
  onRemoveModule: (index: number) => void;
  onStartGeneration: () => void;
  generationStatus: 'idle' | 'generating' | 'completed' | 'error';
}

// 模块类型选项
const MODULE_TYPE_OPTIONS = [
  {
    value: 'basic_usage',
    label: '基础使用数据',
    description: '基础使用数据、当天数据趋势、action数据统计',
    icon: BarChart3,
    color: 'text-blue-600'
  },
  {
    value: 'event_analysis',
    label: '事件分析',
    description: '当日亮点事件和预警事件',
    icon: AlertTriangle,
    color: 'text-orange-600'
  },
  {
    value: 'service_quality',
    label: '服务质量',
    description: '当日满意服务案例和不满意的案例',
    icon: Star,
    color: 'text-yellow-600'
  },
  {
    value: 'insight_analysis',
    label: '洞察分析',
    description: '一些新视角的观察',
    icon: TrendingUp,
    color: 'text-green-600'
  },
  {
    value: 'business_advice',
    label: '业务建议',
    description: '提出一些经营建议',
    icon: Lightbulb,
    color: 'text-purple-600'
  }
];

// 数据源选项
const DATA_SOURCE_OPTIONS = [
  { value: 'statistics', label: '统计数据', description: '从统计表获取聚合数据' },
  { value: 'conversation', label: '会话数据', description: '从会话表获取原始对话数据' },
  { value: 'query', label: '查询数据', description: '从查询日志获取用户查询数据' },
  { value: 'human_stats', label: '人脸识别数据', description: '从人脸识别系统获取访客数据' },
  { value: 'mixed', label: '混合数据源', description: '组合多个数据源的数据' }
];

// 执行模式选项
const EXECUTION_MODE_OPTIONS = [
  { value: 'direct_output', label: '直接输出', description: '直接输出处理后的数据' },
  { value: 'template_render', label: '模板渲染', description: '使用模板渲染数据' },
  { value: 'chart_display', label: '图表展示', description: '生成图表配置和展示' },
  { value: 'llm_generate', label: 'AI生成', description: '使用大语言模型生成内容' }
];

const ModuleConfigPanel: React.FC<ModuleConfigPanelProps> = ({
  isOpen,
  onClose,
  moduleConfigs,
  onModuleConfigChange,
  onAddModule,
  onRemoveModule,
  onStartGeneration,
  generationStatus
}) => {
  const [expandedModules, setExpandedModules] = useState<Set<number>>(new Set());
  const [selectedModuleType, setSelectedModuleType] = useState<string>('basic_usage');

  // 切换模块展开状态
  const toggleModuleExpanded = (index: number) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedModules(newExpanded);
  };

  // 处理模块配置字段变化
  const handleConfigFieldChange = (
    index: number, 
    field: keyof ModuleConfig, 
    value: any
  ) => {
    const config = { ...moduleConfigs[index], [field]: value };
    onModuleConfigChange(index, config);
  };

  // 添加新模块
  const handleAddNewModule = () => {
    const selectedType = MODULE_TYPE_OPTIONS.find(opt => opt.value === selectedModuleType);
    if (!selectedType) return;

    const newConfig: ModuleConfig = {
      module_type: selectedType.value,
      module_name: selectedType.label,
      module_description: selectedType.description,
      data_sources: {},
      primary_data_source: 'statistics',
      execution_mode: 'llm_generate',
      use_llm: true,
      custom_config: {},
      prompt_variables: {}
    };

    onAddModule(newConfig);
  };

  // 获取模块类型信息
  const getModuleTypeInfo = (moduleType: string) => {
    return MODULE_TYPE_OPTIONS.find(opt => opt.value === moduleType) || MODULE_TYPE_OPTIONS[0];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <Settings className="h-6 w-6" />
                模块配置
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                配置报告生成的各个模块，支持自定义数据源和执行模式
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* 添加模块区域 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Plus className="h-5 w-5" />
                添加新模块
              </h3>
              
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <select
                    value={selectedModuleType}
                    onChange={(e) => setSelectedModuleType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {MODULE_TYPE_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label} - {option.description}
                      </option>
                    ))}
                  </select>
                </div>
                <button
                  onClick={handleAddNewModule}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  添加
                </button>
              </div>
            </div>

            {/* 模块列表 */}
            <div className="space-y-4">
              {moduleConfigs.map((config, index) => {
                const moduleTypeInfo = getModuleTypeInfo(config.module_type);
                const isExpanded = expandedModules.has(index);
                const ModuleIcon = moduleTypeInfo.icon;

                return (
                  <div key={index} className="border border-gray-200 rounded-lg">
                    {/* 模块头部 */}
                    <div className="p-4 bg-gray-50 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => toggleModuleExpanded(index)}
                            className="p-1 hover:bg-gray-200 rounded transition-colors"
                          >
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </button>
                          
                          <div className={`p-2 rounded-lg bg-white border`}>
                            <ModuleIcon className={`h-5 w-5 ${moduleTypeInfo.color}`} />
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {config.module_name}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {config.module_description}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                            {config.execution_mode}
                          </span>
                          <button
                            onClick={() => onRemoveModule(index)}
                            className="p-1 hover:bg-red-100 text-red-600 rounded transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* 模块详细配置 */}
                    {isExpanded && (
                      <div className="p-4 space-y-4">
                        {/* 基础配置 */}
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              模块名称
                            </label>
                            <input
                              type="text"
                              value={config.module_name}
                              onChange={(e) => handleConfigFieldChange(index, 'module_name', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              主要数据源
                            </label>
                            <select
                              value={config.primary_data_source}
                              onChange={(e) => handleConfigFieldChange(index, 'primary_data_source', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              {DATA_SOURCE_OPTIONS.map(option => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              执行模式
                            </label>
                            <select
                              value={config.execution_mode}
                              onChange={(e) => handleConfigFieldChange(index, 'execution_mode', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              {EXECUTION_MODE_OPTIONS.map(option => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>
                          
                          <div className="flex items-center">
                            <label className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={config.use_llm}
                                onChange={(e) => handleConfigFieldChange(index, 'use_llm', e.target.checked)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm font-medium text-gray-700">
                                使用AI生成
                              </span>
                            </label>
                          </div>
                        </div>

                        {/* 模块描述 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            模块描述
                          </label>
                          <textarea
                            value={config.module_description}
                            onChange={(e) => handleConfigFieldChange(index, 'module_description', e.target.value)}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        {/* AI提示词配置 */}
                        {config.use_llm && (
                          <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <h5 className="font-medium text-gray-900 flex items-center gap-2">
                              <Brain className="h-4 w-4" />
                              AI提示词配置
                            </h5>
                            
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                系统提示词
                              </label>
                              <textarea
                                value={config.system_prompt || ''}
                                onChange={(e) => handleConfigFieldChange(index, 'system_prompt', e.target.value)}
                                rows={3}
                                placeholder="留空使用默认系统提示词"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                用户提示词
                              </label>
                              <textarea
                                value={config.user_prompt || ''}
                                onChange={(e) => handleConfigFieldChange(index, 'user_prompt', e.target.value)}
                                rows={3}
                                placeholder="留空使用默认用户提示词"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* 空状态 */}
            {moduleConfigs.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                <Settings className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">暂无模块配置</p>
                <p className="text-sm">请添加至少一个模块来开始生成报告</p>
              </div>
            )}
          </div>

          {/* 底部操作区域 */}
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                已配置 {moduleConfigs.length} 个模块
              </div>
              
              <div className="flex items-center gap-3">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                
                <button
                  onClick={onStartGeneration}
                  disabled={moduleConfigs.length === 0 || generationStatus === 'generating'}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {generationStatus === 'generating' ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      开始生成报告
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleConfigPanel;
