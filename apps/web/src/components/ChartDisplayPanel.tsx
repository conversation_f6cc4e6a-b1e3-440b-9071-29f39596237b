import React, { useEffect, useRef, useState } from 'react';
import { BarChart3, TrendingUp, Pie<PERSON><PERSON>, LineC<PERSON>, Download, Maximize2, X } from 'lucide-react';

interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area';
  title: string;
  data: any;
  options?: any;
  clickable?: boolean;
  sessionData?: any[];
}

interface ChartDisplayPanelProps {
  chartConfig: ChartConfig;
  onDataPointClick?: (dataPoint: any, sessionData?: any[]) => void;
  className?: string;
}

// 图表类型图标映射
const CHART_TYPE_ICONS = {
  line: LineChart,
  bar: BarChart3,
  pie: PieChart,
  scatter: TrendingUp,
  area: TrendingUp
};

const ChartDisplayPanel: React.FC<ChartDisplayPanelProps> = ({
  chartConfig,
  onDataPointClick,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<any>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 动态加载ECharts
  useEffect(() => {
    const loadECharts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 动态导入ECharts
        const echarts = await import('echarts');
        
        if (chartRef.current && chartConfig) {
          // 销毁现有图表实例
          if (chartInstance) {
            chartInstance.dispose();
          }

          // 创建新的图表实例
          const chart = echarts.init(chartRef.current);
          
          // 转换配置为ECharts格式
          const option = convertToEChartsOption(chartConfig);
          
          // 设置图表配置
          chart.setOption(option);
          
          // 添加点击事件监听
          if (chartConfig.clickable && onDataPointClick) {
            chart.on('click', (params: any) => {
              const sessionData = chartConfig.sessionData || [];
              onDataPointClick(params, sessionData);
            });
          }

          // 响应式调整
          const resizeObserver = new ResizeObserver(() => {
            chart.resize();
          });
          resizeObserver.observe(chartRef.current);

          setChartInstance(chart);
          setIsLoading(false);

          // 清理函数
          return () => {
            resizeObserver.disconnect();
            chart.dispose();
          };
        }
      } catch (err) {
        console.error('加载ECharts失败:', err);
        setError('图表加载失败');
        setIsLoading(false);
      }
    };

    loadECharts();
  }, [chartConfig, onDataPointClick]);

  // 转换图表配置为ECharts格式
  const convertToEChartsOption = (config: ChartConfig) => {
    const baseOption = {
      title: {
        text: config.title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      toolbox: {
        feature: {
          saveAsImage: {
            title: '保存为图片'
          },
          dataZoom: {
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原'
            }
          }
        }
      }
    };

    switch (config.type) {
      case 'line':
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: config.data.labels || [],
            boundaryGap: false
          },
          yAxis: {
            type: 'value'
          },
          series: (config.data.datasets || []).map((dataset: any) => ({
            name: dataset.label,
            type: 'line',
            data: dataset.data || [],
            smooth: true,
            lineStyle: {
              color: dataset.borderColor || '#3b82f6'
            },
            areaStyle: dataset.fill ? {
              color: dataset.backgroundColor || 'rgba(59, 130, 246, 0.1)'
            } : undefined
          }))
        };

      case 'bar':
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: config.data.labels || []
          },
          yAxis: {
            type: 'value'
          },
          series: (config.data.datasets || []).map((dataset: any) => ({
            name: dataset.label,
            type: 'bar',
            data: dataset.data || [],
            itemStyle: {
              color: dataset.backgroundColor || '#3b82f6'
            }
          }))
        };

      case 'pie':
        return {
          ...baseOption,
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [{
            name: config.title,
            type: 'pie',
            radius: '50%',
            data: (config.data.labels || []).map((label: string, index: number) => ({
              value: config.data.datasets?.[0]?.data?.[index] || 0,
              name: label
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };

      case 'scatter':
        return {
          ...baseOption,
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'value'
          },
          series: (config.data.datasets || []).map((dataset: any) => ({
            name: dataset.label,
            type: 'scatter',
            data: dataset.data || [],
            symbolSize: 8,
            itemStyle: {
              color: dataset.backgroundColor || '#3b82f6'
            }
          }))
        };

      default:
        return baseOption;
    }
  };

  // 导出图表
  const exportChart = () => {
    if (chartInstance) {
      const url = chartInstance.getDataURL({
        type: 'png',
        backgroundColor: '#fff'
      });
      
      const link = document.createElement('a');
      link.download = `${chartConfig.title}.png`;
      link.href = url;
      link.click();
    }
  };

  // 全屏切换
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const ChartIcon = CHART_TYPE_ICONS[chartConfig.type] || BarChart3;

  return (
    <>
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
        {/* 图表头部 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ChartIcon className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                {chartConfig.title}
              </h3>
              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                {chartConfig.type.toUpperCase()}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              {chartConfig.clickable && (
                <span className="text-xs text-gray-500">
                  点击数据点查看详情
                </span>
              )}
              
              <button
                onClick={exportChart}
                disabled={isLoading || error}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="导出图表"
              >
                <Download className="h-4 w-4" />
              </button>
              
              <button
                onClick={toggleFullscreen}
                disabled={isLoading || error}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="全屏显示"
              >
                <Maximize2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* 图表内容 */}
        <div className="p-4">
          <div className="relative">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">加载图表中...</p>
                </div>
              </div>
            )}
            
            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50 rounded">
                <div className="text-center text-red-600">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm font-medium">{error}</p>
                </div>
              </div>
            )}
            
            <div
              ref={chartRef}
              className="w-full h-96"
              style={{ minHeight: '400px' }}
            />
          </div>
        </div>

        {/* 图表说明 */}
        {chartConfig.clickable && (
          <div className="px-4 pb-4">
            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded border border-blue-200">
              💡 提示：点击图表中的数据点可以查看相关的会话详情
            </div>
          </div>
        )}
      </div>

      {/* 全屏模态框 */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
          <div className="w-full h-full max-w-7xl max-h-full p-8">
            <div className="bg-white rounded-lg h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <ChartIcon className="h-6 w-6 text-blue-600" />
                  {chartConfig.title}
                </h3>
                <button
                  onClick={toggleFullscreen}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="flex-1 p-4">
                <div
                  ref={chartRef}
                  className="w-full h-full"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChartDisplayPanel;
