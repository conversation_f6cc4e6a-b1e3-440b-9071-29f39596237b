-- 模块化报告系统数据库表创建脚本
-- 创建时间: 2025-07-31

-- 1. 扩展现有设备报告表，添加模块化支持
ALTER TABLE aos_device_reports 
ADD COLUMN is_modular BOOLEAN DEFAULT FALSE COMMENT '是否为模块化报告',
ADD COLUMN module_config JSON COMMENT '模块配置信息',
ADD COLUMN total_modules INT DEFAULT 0 COMMENT '总模块数',
ADD COLUMN completed_modules INT DEFAULT 0 COMMENT '已完成模块数',
ADD INDEX idx_is_modular (is_modular);

-- 2. 创建报告模块执行记录表
CREATE TABLE aos_report_modules (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    report_id INT NOT NULL COMMENT '报告ID',
    module_type VARCHAR(50) NOT NULL COMMENT '模块类型',
    module_name VARCHAR(100) NOT NULL COMMENT '模块名称',
    module_description TEXT COMMENT '模块描述',
    execution_order INT NOT NULL COMMENT '执行顺序',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '模块状态',
    
    -- 数据源配置
    data_sources JSON COMMENT '数据源配置',
    primary_data_source VARCHAR(50) COMMENT '主要数据源类型',
    
    -- 执行配置
    execution_mode VARCHAR(50) DEFAULT 'llm_generate' COMMENT '执行模式',
    use_llm BOOLEAN DEFAULT TRUE COMMENT '是否使用大模型',
    template_name VARCHAR(100) COMMENT '模板名称',
    custom_config JSON COMMENT '自定义配置',
    
    -- Prompt配置
    system_prompt TEXT COMMENT '系统Prompt',
    user_prompt TEXT COMMENT '用户Prompt',
    prompt_variables JSON COMMENT 'Prompt变量',
    
    -- 执行结果
    raw_data JSON COMMENT '原始数据',
    processed_data JSON COMMENT '处理后数据',
    generated_content TEXT COMMENT '生成的内容',
    chart_config JSON COMMENT '图表配置',
    interactive_data JSON COMMENT '交互数据(如session链接)',
    
    -- 性能指标
    data_fetch_duration INT COMMENT '数据获取耗时(毫秒)',
    processing_duration INT COMMENT '数据处理耗时(毫秒)',
    generation_duration INT COMMENT '内容生成耗时(毫秒)',
    total_duration INT COMMENT '总耗时(毫秒)',
    tokens_used INT COMMENT '使用的Token数量',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    error_details JSON COMMENT '错误详情',
    
    -- 元数据
    started_at DATETIME COMMENT '开始执行时间',
    completed_at DATETIME COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (report_id) REFERENCES aos_device_reports(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_report_id_order (report_id, execution_order),
    INDEX idx_module_type_status (module_type, status),
    INDEX idx_execution_mode (execution_mode),
    INDEX idx_primary_data_source (primary_data_source),
    INDEX idx_created_at (created_at)
) COMMENT='报告模块执行记录表';

-- 3. 创建模块模板配置表
CREATE TABLE aos_module_templates (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    template_name VARCHAR(100) NOT NULL UNIQUE COMMENT '模板名称',
    module_type VARCHAR(50) NOT NULL COMMENT '模块类型',
    template_description TEXT COMMENT '模板描述',
    
    -- 模板配置
    execution_mode VARCHAR(50) DEFAULT 'llm_generate' COMMENT '执行模式',
    data_source_config JSON COMMENT '数据源配置',
    prompt_template TEXT COMMENT 'Prompt模板',
    output_template TEXT COMMENT '输出模板',
    chart_template JSON COMMENT '图表模板',
    
    -- 变量定义
    required_variables JSON COMMENT '必需变量列表',
    optional_variables JSON COMMENT '可选变量列表',
    default_values JSON COMMENT '默认值配置',
    
    -- 状态和版本
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '模板版本',
    
    -- 元数据
    created_by VARCHAR(100) COMMENT '创建者',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_template_name (template_name),
    INDEX idx_module_type_active (module_type, is_active),
    INDEX idx_execution_mode (execution_mode)
) COMMENT='模块模板配置表';

-- 4. 创建数据源配置表
CREATE TABLE aos_data_source_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
    data_source_type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    config_description TEXT COMMENT '配置描述',
    
    -- 数据源配置
    connection_config JSON COMMENT '连接配置',
    query_config JSON COMMENT '查询配置',
    filter_config JSON COMMENT '过滤配置',
    aggregation_config JSON COMMENT '聚合配置',
    
    -- 缓存配置
    enable_cache BOOLEAN DEFAULT TRUE COMMENT '是否启用缓存',
    cache_duration INT DEFAULT 3600 COMMENT '缓存时长(秒)',
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    -- 元数据
    created_by VARCHAR(100) COMMENT '创建者',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_config_name (config_name),
    INDEX idx_data_source_type_active (data_source_type, is_active)
) COMMENT='数据源配置表';

-- 5. 插入默认模块模板
INSERT INTO aos_module_templates (template_name, module_type, template_description, execution_mode, data_source_config, prompt_template, required_variables, default_values, created_by) VALUES
('基础使用数据模板', 'basic_usage', '基础使用数据、当天数据趋势、action数据统计', 'llm_generate', 
 '{"primary": "statistics", "secondary": ["conversation", "query"]}',
 '请基于以下数据生成基础使用数据报告：\n数据时间范围：{date_range}\n统计数据：{statistics_data}\n会话数据：{conversation_data}',
 '["date_range", "statistics_data"]', 
 '{"chart_type": "line", "show_trends": true}', 
 'system'),

('事件分析模板', 'event_analysis', '当日亮点事件和预警事件分析', 'llm_generate',
 '{"primary": "conversation", "secondary": ["statistics"]}',
 '请分析以下数据中的亮点事件和预警事件：\n时间范围：{date_range}\n事件数据：{event_data}\n统计数据：{statistics_data}',
 '["date_range", "event_data"]',
 '{"highlight_threshold": 0.8, "warning_threshold": 0.3}',
 'system'),

('服务质量模板', 'service_quality', '当日满意和不满意服务案例分析', 'llm_generate',
 '{"primary": "conversation", "secondary": ["human_stats"]}',
 '请分析以下服务案例的满意度：\n时间范围：{date_range}\n服务案例：{service_cases}\n用户反馈：{user_feedback}',
 '["date_range", "service_cases"]',
 '{"satisfaction_threshold": 4.0, "show_cases": true}',
 'system'),

('洞察分析模板', 'insight_analysis', '新视角的观察和分析', 'llm_generate',
 '{"primary": "mixed", "secondary": ["statistics", "conversation", "query"]}',
 '请从新的视角分析以下数据，提供深度洞察：\n数据范围：{date_range}\n综合数据：{mixed_data}\n历史对比：{historical_data}',
 '["date_range", "mixed_data"]',
 '{"analysis_depth": "deep", "include_predictions": true}',
 'system'),

('经营建议模板', 'business_advice', '基于数据的经营建议', 'llm_generate',
 '{"primary": "statistics", "secondary": ["conversation", "human_stats"]}',
 '请基于以下数据提供经营建议：\n业务数据：{business_data}\n用户行为：{user_behavior}\n运营指标：{operation_metrics}',
 '["business_data", "user_behavior"]',
 '{"advice_type": "actionable", "priority_level": "high"}',
 'system');

-- 6. 插入默认数据源配置
INSERT INTO aos_data_source_configs (config_name, data_source_type, config_description, connection_config, query_config, created_by) VALUES
('统计数据源', 'statistics', '从统计表获取聚合数据', 
 '{"database": "speech_ai_robot", "tables": ["aos_stat_*"]}',
 '{"time_range": "1d", "aggregation": "hourly", "metrics": ["session", "message", "action"]}',
 'system'),

('会话数据源', 'conversation', '从会话表获取原始对话数据',
 '{"database": "agentos_online", "tables": ["aos_conversation_sessions", "aos_conversation_messages"]}',
 '{"include_messages": true, "filter_status": "completed", "max_records": 1000}',
 'system'),

('查询数据源', 'query', '从查询日志获取用户查询数据',
 '{"database": "bigdata_cn", "tables": ["query_logs"]}',
 '{"include_metadata": true, "filter_device": true, "time_bucket": "hour"}',
 'system'),

('人脸识别数据源', 'human_stats', '从人脸识别系统获取访客数据',
 '{"database": "agentos_online", "tables": ["human_detection_*"]}',
 '{"include_demographics": true, "privacy_mode": true, "aggregation": "daily"}',
 'system'),

('混合数据源', 'mixed', '组合多个数据源的数据',
 '{"sources": ["statistics", "conversation", "query"], "merge_strategy": "time_based"}',
 '{"correlation_analysis": true, "cross_reference": true, "data_quality_check": true}',
 'system');
