# -*- coding: utf-8 -*-
"""
设备报告生成服务 - 模块化版本
支持模块化报告生成、数据源选择、模板定制、大模型可选
"""

import logging
import os
import json
from typing import AsyncGenerator, Optional, Dict, Any, List
import asyncio
import time
from datetime import datetime

from app.config.report_prompts import ReportPrompts
from app.services.llm_service import LLMService
from app.services.unified_data_service import UnifiedDataService
from app.services.modular_report_helpers import ModularReportHelpers
from app.models.report_models import (
    ReportType, PromptType, ModuleType, DataSourceType,
    ExecutionMode, ModuleStatus
)
from app.database import get_agentos_db, get_speech_ai_robot_db

logger = logging.getLogger(__name__)


class DeviceReportService:
    """设备报告生成服务 - 支持传统和模块化报告生成"""

    def __init__(self, report_data_service=None):
        """
        初始化设备报告服务

        Args:
            report_data_service: 报告数据服务实例，用于数据库操作
        """
        self.llm_service = None
        self.report_data_service = report_data_service
        self.unified_data_service = None
        self.modular_helpers = None
    
    def _get_llm_service(self, model_name: str = None):
        """延迟初始化LLM服务"""
        if self.llm_service is None or (model_name and self.llm_service.model_name != model_name):
            # 使用指定的模型名称或默认模型
            self.llm_service = LLMService(model_name=model_name)
        return self.llm_service
    
    def _load_chat_data(self, enterprise_id: str, device_id: str, date: str) -> str:
        """
        从data目录加载指定日期的聊天记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID  
            date: 日期 (YYYY-MM-DD格式)
            
        Returns:
            聊天记录文本
        """
        try:
            # 构建文件路径
            data_dir = os.path.join("app", "data", enterprise_id, device_id)
            
            # 查找匹配的文件
            if os.path.exists(data_dir):
                for filename in os.listdir(data_dir):
                    if filename.endswith(f"_chats数据_{date}.txt"):
                        file_path = os.path.join(data_dir, filename)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        logger.info(f"成功加载聊天数据: {file_path}")
                        return content
            
            # 如果找不到文件，返回空数据提示
            logger.warning(f"未找到日期 {date} 的聊天数据文件")
            return f"未找到企业 {enterprise_id} 设备 {device_id} 在 {date} 的聊天记录数据。"
            
        except Exception as e:
            logger.error(f"加载聊天数据失败: {str(e)}")
            return f"加载聊天数据时发生错误: {str(e)}"
    
    async def generate_single_day_report(
        self,
        enterprise_id: str,
        device_id: str, 
        date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2",
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        model_name: Optional[str] = None,
        report_id: Optional[int] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        生成单天设备使用报告
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            date: 日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        try:
            logger.info(f"开始生成单天设备报告: 企业={enterprise_id}, 设备={device_id}, 日期={date}, Prompt版本={prompt_version}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 加载聊天数据
            chat_data = self._load_chat_data(enterprise_id, device_id, date)
            
            # 检查数据是否有效
            is_valid_data = (
                chat_data and 
                len(chat_data.strip()) > 0 and
                "未找到企业" not in chat_data and 
                "加载聊天数据时发生错误" not in chat_data
            )
            
            # 发送报告开始信息
            yield (f"# 🚀 设备使用报告（单天）\n\n", None)
            yield (f"**企业ID**: {enterprise_id}\n", None)
            yield (f"**设备ID**: {device_id}\n", None)
            yield (f"**日期**: {date}\n", None)
            yield (f"**Prompt版本**: {prompt_version}\n", None)
            yield (f"**数据状态**: {'✅ 有效' if is_valid_data else '❌ 无效'}\n\n", None)
            yield ("---\n\n", None)
            
            # 如果数据无效，直接返回提示信息
            if not is_valid_data:
                yield (f"⚠️ **数据检查结果**\n\n", None)
                yield (f"在 {date} 未找到有效的聊天记录数据。\n\n", None)
                yield (f"**原始数据内容**:\n", None)
                yield (f"```\n{chat_data}\n```\n\n", None)
                yield (f"**建议操作**:\n", None)
                yield (f"- 检查设备ID和企业ID是否正确\n", None)
                yield (f"- 尝试选择其他日期\n", None)
                yield (f"- 联系技术支持确认数据状态\n\n", None)
                return
            
            # 确定使用的Prompt
            if custom_system_prompt:
                # 使用统一的SYSTEM_PROMPT作为基础，结合自定义内容
                base_system_prompt = ReportPrompts.get_system_prompt()
                system_prompt = f"{base_system_prompt}\n\n【自定义补充要求】\n{custom_system_prompt}"
                prompt_type = PromptType.CUSTOM
                logger.info(f"🔧 单天报告: 使用自定义系统Prompt，长度: {len(system_prompt)}")
            else:
                system_prompt = ReportPrompts.get_system_prompt()
                prompt_type = PromptType.SYSTEM
                logger.info(f"🔧 单天报告: 使用默认系统Prompt，长度: {len(system_prompt)}")
            
            # 创建数据信息
            data_info = ReportPrompts.create_single_day_data_info(
                date=date,
                chat_data=chat_data,
                description=f"以下是 {date} 的设备使用原始数据："
            )
            
            # 确定用户提示词
            if custom_user_prompt:
                # 使用自定义用户提示词，需要替换数据占位符
                user_prompt = custom_user_prompt.replace("{data_description}", data_info.get('description', ''))
                user_prompt = user_prompt.replace("{analysis_data}", data_info.get('data_sources', [{}])[0].get('content', ''))
                prompt_type = PromptType.CUSTOM
            else:
                # 根据版本获取用户提示词
                user_prompt = ReportPrompts.get_unified_prompt(data_info, prompt_version)
                prompt_type = PromptType.SYSTEM
            
            # 打印关键统计信息
            logger.info(f"📊 单天报告生成准备完成:")
            logger.info(f"   - 聊天数据长度: {len(chat_data)} 字符")
            logger.info(f"   - 提示词长度: {len(user_prompt)} 字符")
            logger.info(f"   - 使用的Prompt版本: {prompt_version}")
            logger.info(f"   - 使用的模型: {model_name or '默认模型'}")
            
            # 发送LLM开始标记
            yield ("\n\n🚀 **开始生成AI报告内容**\n\n", None)
            yield ("---\n\n", None)
            
            # 调用LLM生成完整内容
            llm_service = self._get_llm_service(model_name)
            response_content = ""
            chunk_count = 0
            tokens_used = 0
            
            async for result in llm_service.generate_report_stage(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                stage_info={"name": "单天设备使用报告", "prompt_version": prompt_version}
            ):
                content_chunk, token_info = result
                
                if content_chunk is not None:
                    # 这是内容块
                    response_content += content_chunk
                    chunk_count += 1
                    yield (content_chunk, None)
                elif token_info is not None:
                    # 这是token信息
                    tokens_used = token_info.get('tokens_used', 0)
                    logger.info(f"📊 获取到Token使用量: {tokens_used}")
            
            # 更新报告完成信息（如果启用了数据库记录）
            if self.report_data_service and report_id:
                logger.info(f"开始更新报告完成信息: report_id={report_id}, chunk_count={chunk_count}")
                try:
                    self.report_data_service.update_report_completion(
                        report_id=report_id,
                        system_prompt_used=system_prompt,
                        user_prompt_used=user_prompt,
                        generated_content=response_content,
                        model_name=llm_service.model_name,
                        chunk_count=chunk_count,
                        tokens_used=tokens_used,
                        prompt_type=prompt_type
                    )
                    logger.info(f"报告完成信息更新成功: report_id={report_id}, tokens_used={tokens_used}, prompt_type={prompt_type.value}")
                except Exception as e:
                    logger.error(f"更新报告完成信息失败: {str(e)}")
                    raise
            else:
                logger.warning(f"跳过报告完成信息更新: report_data_service={self.report_data_service is not None}, report_id={report_id}")
            
            # 发送LLM结束标记
            yield ("\n\n---\n\n", None)
            yield ("✅ **AI报告内容生成完成**\n\n", None)
            
        except Exception as e:
            logger.error(f"生成单天报告失败: {str(e)}")
            yield (f"\n\n❌ 生成单天报告时发生错误: {str(e)}\n\n", None)

    async def generate_multi_day_report(
        self,
        enterprise_id: str,
        device_id: str,
        start_date: str,
        end_date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2",
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        model_name: Optional[str] = None,
        report_id: Optional[int] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        生成多天设备使用报告
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        try:
            logger.info(f"开始生成多天设备报告: 企业={enterprise_id}, 设备={device_id}, 日期范围={start_date}至{end_date}, Prompt版本={prompt_version}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 加载多天聊天数据
            data_sources = []
            from datetime import datetime, timedelta
            
            # 生成日期范围
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            current_dt = start_dt
            
            logger.info(f"📅 开始扫描日期范围: {start_date} 至 {end_date}")
            
            while current_dt <= end_dt:
                current_date = current_dt.strftime("%Y-%m-%d")
                logger.info(f"🔍 检查日期: {current_date}")
                
                chat_data = self._load_chat_data(enterprise_id, device_id, current_date)
                
                # 检查数据是否有效（不是错误信息且不为空）
                is_valid_data = (
                    chat_data and 
                    len(chat_data.strip()) > 0 and
                    "未找到企业" not in chat_data and 
                    "加载聊天数据时发生错误" not in chat_data
                )
                
                if is_valid_data:
                    logger.info(f"✅ 找到有效数据: {current_date}, 长度: {len(chat_data)} 字符")
                    data_sources.append({
                        'date': current_date,
                        'content': chat_data
                    })
                else:
                    logger.warning(f"❌ 日期 {current_date} 无有效数据: {chat_data[:100]}...")
                
                current_dt += timedelta(days=1)
            
            logger.info(f"📊 数据扫描完成: 找到 {len(data_sources)} 天的有效数据")
            
            if not data_sources:
                yield (f"# ❌ 多天报告生成失败\n\n", None)
                yield (f"**企业ID**: {enterprise_id}\n", None)
                yield (f"**设备ID**: {device_id}\n", None)
                yield (f"**日期范围**: {start_date} 至 {end_date}\n", None)
                yield (f"**数据状态**: ❌ 无效\n\n", None)
                yield (f"在指定日期范围内未找到任何有效的聊天数据。\n\n", None)
                yield (f"**扫描结果**:\n", None)
                yield (f"- 扫描的日期范围: {start_date} 至 {end_date}\n", None)
                yield (f"- 有效数据天数: 0 天\n", None)
                yield (f"- 所有日期均无有效数据\n\n", None)
                yield (f"**可能的原因**:\n", None)
                yield (f"- 该设备在指定日期范围内没有产生聊天记录\n", None)
                yield (f"- 数据文件不存在或路径错误\n", None)
                yield (f"- 数据文件格式不正确\n\n", None)
                yield (f"**建议操作**:\n", None)
                yield (f"- 检查设备ID和企业ID是否正确\n", None)
                yield (f"- 尝试选择其他日期范围\n", None)
                yield (f"- 联系技术支持确认数据状态\n\n", None)
                yield (f"**调试信息**:\n", None)
                yield (f"- 数据文件路径: app/data/{enterprise_id}/{device_id}/\n", None)
                yield (f"- 文件命名格式: *_chats数据_YYYY-MM-DD.txt\n\n", None)
                return
            
                        # 发送报告开始信息
            yield (f"# 🚀 设备使用报告（多天）\n\n", None)
            yield (f"**企业ID**: {enterprise_id}\n", None)
            yield (f"**设备ID**: {device_id}\n", None)
            yield (f"**日期范围**: {start_date} 至 {end_date}\n", None)
            yield (f"**数据天数**: {len(data_sources)} 天\n", None)
            yield (f"**有效日期**: {', '.join([source['date'] for source in data_sources])}\n", None)
            yield (f"**Prompt版本**: {prompt_version}\n", None)
            yield (f"**数据状态**: ✅ 有效\n\n", None)
            yield ("---\n\n", None)
            
            # 确定使用的Prompt
            if custom_system_prompt:
                # 使用统一的SYSTEM_PROMPT作为基础，结合自定义内容
                base_system_prompt = ReportPrompts.get_system_prompt()
                system_prompt = f"{base_system_prompt}\n\n【自定义补充要求】\n{custom_system_prompt}"
                prompt_type = PromptType.CUSTOM
                logger.info(f"🔧 多天报告: 使用自定义系统Prompt，长度: {len(system_prompt)}")
            else:
                system_prompt = ReportPrompts.get_system_prompt()
                prompt_type = PromptType.SYSTEM
                logger.info(f"🔧 多天报告: 使用默认系统Prompt，长度: {len(system_prompt)}")
            
            # 创建多天数据信息
            data_info = ReportPrompts.create_multi_day_data_info(
                data_sources=data_sources,
                description=f"以下是 {start_date} 至 {end_date} 期间的设备使用原始数据（共{len(data_sources)}天）："
            )
            
            # 确定用户提示词
            if custom_user_prompt:
                # 使用自定义用户提示词，需要替换数据占位符
                user_prompt = custom_user_prompt.replace("{data_description}", data_info.get('description', ''))
                prompt_type = PromptType.CUSTOM
                # 格式化多天数据
                formatted_data = ""
                for source in data_info.get('data_sources', []):
                    formatted_data += f"【{source['date']} 数据】\n{source['content']}\n\n"
                user_prompt = user_prompt.replace("{analysis_data}", formatted_data.strip())
            else:
                # 根据版本获取用户提示词
                user_prompt = ReportPrompts.get_unified_prompt(data_info, prompt_version)
                prompt_type = PromptType.SYSTEM
            
            # 打印关键统计信息
            total_chars = sum(len(source['content']) for source in data_sources)
            logger.info(f"📊 多天报告生成准备完成:")
            logger.info(f"   - 数据天数: {len(data_sources)} 天")
            logger.info(f"   - 有效日期: {[source['date'] for source in data_sources]}")
            logger.info(f"   - 总聊天数据长度: {total_chars} 字符")
            logger.info(f"   - 提示词长度: {len(user_prompt)} 字符")
            logger.info(f"   - 使用的Prompt版本: {prompt_version}")
            logger.info(f"   - 使用的模型: {model_name or '默认模型'}")
            
            # 发送LLM开始标记
            yield ("\n\n🚀 **开始生成AI报告内容**\n\n", None)
            yield ("---\n\n", None)
            
            # 调用LLM生成完整内容
            llm_service = self._get_llm_service(model_name)
            response_content = ""
            chunk_count = 0
            tokens_used = 0
            
            async for result in llm_service.generate_report_stage(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                stage_info={"name": "多天设备使用报告", "prompt_version": prompt_version}
            ):
                content_chunk, token_info = result
                
                if content_chunk is not None:
                    # 这是内容块
                    response_content += content_chunk
                    chunk_count += 1
                    yield (content_chunk, None)
                elif token_info is not None:
                    # 这是token信息
                    tokens_used = token_info.get('tokens_used', 0)
                    logger.info(f"📊 获取到多天报告Token使用量: {tokens_used}")
            
            # 更新报告完成信息（如果启用了数据库记录）
            if self.report_data_service and report_id:
                logger.info(f"开始更新多天报告完成信息: report_id={report_id}, chunk_count={chunk_count}")
                try:
                    self.report_data_service.update_report_completion(
                        report_id=report_id,
                        system_prompt_used=system_prompt,
                        user_prompt_used=user_prompt,
                        generated_content=response_content,
                        model_name=llm_service.model_name,
                        chunk_count=chunk_count,
                        tokens_used=tokens_used,
                        prompt_type=prompt_type
                    )
                    logger.info(f"多天报告完成信息更新成功: report_id={report_id}, tokens_used={tokens_used}, prompt_type={prompt_type.value}")
                except Exception as e:
                    logger.error(f"更新多天报告完成信息失败: {str(e)}")
                    raise
            else:
                logger.warning(f"跳过多天报告完成信息更新: report_data_service={self.report_data_service is not None}, report_id={report_id}")
            
            # 发送LLM结束标记
            yield ("\n\n---\n\n", None)
            yield ("✅ **AI报告内容生成完成**\n\n", None)
            
        except Exception as e:
            logger.error(f"生成多天报告失败: {str(e)}")
            yield (f"\n\n❌ 生成多天报告时发生错误: {str(e)}\n\n", None)

    async def generate_report(
        self,
        enterprise_id: str,
        device_id: str, 
        date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2"
    ) -> AsyncGenerator[tuple, None]:
        """
        生成设备使用报告（兼容方法，默认单天）
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            date: 日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        # 调用单天报告生成方法
        async for chunk in self.generate_single_day_report(
            enterprise_id=enterprise_id,
            device_id=device_id,
            date=date,
            prompt_version=prompt_version
        ):
            yield chunk

    # ==================== 模块化报告生成方法 ====================

    def _get_unified_data_service(self):
        """延迟初始化统一数据服务"""
        if self.unified_data_service is None:
            agentos_db = next(get_agentos_db())
            speech_ai_robot_db = next(get_speech_ai_robot_db())
            self.unified_data_service = UnifiedDataService(agentos_db, speech_ai_robot_db)
        return self.unified_data_service

    def _get_modular_helpers(self):
        """延迟初始化模块化报告辅助方法"""
        if self.modular_helpers is None:
            unified_service = self._get_unified_data_service()
            self.modular_helpers = ModularReportHelpers(unified_service, self.llm_service)
        return self.modular_helpers

    async def generate_modular_report(
        self,
        enterprise_id: str,
        device_id: str,
        target_date: str,
        module_configs: List[Dict[str, Any]],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        model_name: Optional[str] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        生成模块化报告

        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            target_date: 目标日期
            module_configs: 模块配置列表
            start_date: 开始日期(多天报告)
            end_date: 结束日期(多天报告)
            model_name: 模型名称

        Yields:
            报告内容流 (content, metadata)
        """
        try:
            logger.info(f"开始生成模块化报告: {enterprise_id}/{device_id}, 日期={target_date}, 模块数={len(module_configs)}")

            # 创建报告记录
            report = None
            if self.report_data_service:
                report = self.report_data_service.create_modular_report(
                    enterprise_id=enterprise_id,
                    device_id=device_id,
                    target_date=target_date,
                    module_configs=module_configs,
                    start_date=start_date,
                    end_date=end_date
                )
                yield (f"📋 **创建模块化报告记录**: ID={report.id}, 模块数={len(module_configs)}\n\n", {
                    "type": "report_created",
                    "report_id": report.id,
                    "total_modules": len(module_configs)
                })

            # 获取报告模块列表
            modules = []
            if report:
                modules = self.report_data_service.get_report_modules(report.id)

            # 逐个执行模块
            for i, module_config in enumerate(module_configs):
                module = modules[i] if i < len(modules) else None

                yield (f"🔄 **开始执行模块 {i+1}/{len(module_configs)}**: {module_config['module_name']}\n", {
                    "type": "module_start",
                    "module_index": i,
                    "module_name": module_config['module_name'],
                    "module_id": module.id if module else None
                })

                # 执行单个模块
                async for chunk in self._execute_single_module(
                    enterprise_id=enterprise_id,
                    device_id=device_id,
                    target_date=target_date,
                    start_date=start_date,
                    end_date=end_date,
                    module_config=module_config,
                    module=module,
                    model_name=model_name
                ):
                    yield chunk

                yield (f"✅ **模块 {i+1} 执行完成**: {module_config['module_name']}\n\n", {
                    "type": "module_complete",
                    "module_index": i,
                    "module_name": module_config['module_name']
                })

            # 生成报告总结
            yield ("📊 **模块化报告生成完成**\n\n", {
                "type": "report_complete",
                "total_modules": len(module_configs)
            })

            yield ("---\n\n## 📋 报告总结\n\n", None)
            yield (f"- 总模块数: {len(module_configs)}\n", None)
            yield (f"- 目标日期: {target_date}\n", None)
            if start_date and end_date:
                yield (f"- 日期范围: {start_date} 至 {end_date}\n", None)
            yield (f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n", None)

        except Exception as e:
            logger.error(f"生成模块化报告失败: {str(e)}")
            yield (f"\n\n❌ 生成模块化报告时发生错误: {str(e)}\n\n", {
                "type": "error",
                "error": str(e)
            })

    async def _execute_single_module(
        self,
        enterprise_id: str,
        device_id: str,
        target_date: str,
        start_date: Optional[str],
        end_date: Optional[str],
        module_config: Dict[str, Any],
        module: Optional[Any] = None,
        model_name: Optional[str] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        执行单个模块

        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            target_date: 目标日期
            start_date: 开始日期
            end_date: 结束日期
            module_config: 模块配置
            module: 模块记录对象
            model_name: 模型名称

        Yields:
            模块执行结果流
        """
        module_start_time = time.time()

        try:
            # 更新模块状态为运行中
            if module and self.report_data_service:
                self.report_data_service.update_module_status(
                    module.id, ModuleStatus.RUNNING, started_at=datetime.utcnow()
                )

            # 1. 数据获取阶段
            yield (f"📊 获取数据源: {module_config.get('primary_data_source', 'unknown')}\n", {
                "type": "data_fetch_start",
                "data_source": module_config.get('primary_data_source')
            })

            data_fetch_start = time.time()
            helpers = self._get_modular_helpers()
            raw_data = await helpers.fetch_module_data(
                enterprise_id, device_id, target_date, start_date, end_date, module_config
            )
            data_fetch_duration = int((time.time() - data_fetch_start) * 1000)

            yield (f"✅ 数据获取完成 ({data_fetch_duration}ms)\n", {
                "type": "data_fetch_complete",
                "duration": data_fetch_duration
            })

            # 2. 数据处理阶段
            yield ("🔄 处理数据...\n", {"type": "data_processing_start"})

            processing_start = time.time()
            processed_data = await helpers.process_module_data(raw_data, module_config)
            processing_duration = int((time.time() - processing_start) * 1000)

            yield (f"✅ 数据处理完成 ({processing_duration}ms)\n", {
                "type": "data_processing_complete",
                "duration": processing_duration
            })

            # 3. 内容生成阶段
            execution_mode = module_config.get('execution_mode', ExecutionMode.LLM_GENERATE.value)

            if execution_mode == ExecutionMode.DIRECT_OUTPUT.value:
                # 直接输出模式
                yield ("📝 直接输出结果\n", {"type": "direct_output"})
                content = self._generate_direct_output(processed_data, module_config)
                generation_duration = 0
                tokens_used = 0

            elif execution_mode == ExecutionMode.TEMPLATE_RENDER.value:
                # 模板渲染模式
                yield ("📝 模板渲染\n", {"type": "template_render"})
                content = self._render_template(processed_data, module_config)
                generation_duration = 0
                tokens_used = 0

            elif execution_mode == ExecutionMode.CHART_DISPLAY.value:
                # 图表展示模式
                yield ("📈 生成图表配置\n", {"type": "chart_generation"})
                content, chart_config = self._generate_chart_config(processed_data, module_config)
                generation_duration = 0
                tokens_used = 0

            else:
                # LLM生成模式
                yield ("🤖 AI内容生成中...\n", {"type": "llm_generation_start"})

                generation_start = time.time()
                content, tokens_used = await self._generate_llm_content(
                    processed_data, module_config, model_name
                )
                generation_duration = int((time.time() - generation_start) * 1000)

                yield (f"✅ AI生成完成 ({generation_duration}ms, {tokens_used} tokens)\n", {
                    "type": "llm_generation_complete",
                    "duration": generation_duration,
                    "tokens_used": tokens_used
                })

            # 4. 输出内容
            yield (f"\n### {module_config['module_name']}\n\n", {
                "type": "module_content_start",
                "module_name": module_config['module_name']
            })

            yield (content, {
                "type": "module_content",
                "module_type": module_config['module_type']
            })

            yield ("\n\n", None)

            # 5. 更新模块结果
            total_duration = int((time.time() - module_start_time) * 1000)

            if module and self.report_data_service:
                self.report_data_service.update_module_result(
                    module.id,
                    raw_data=raw_data,
                    processed_data=processed_data,
                    generated_content=content,
                    chart_config=module_config.get('chart_config'),
                    interactive_data=module_config.get('interactive_data'),
                    performance_metrics={
                        'data_fetch_duration': data_fetch_duration,
                        'processing_duration': processing_duration,
                        'generation_duration': generation_duration,
                        'total_duration': total_duration
                    },
                    tokens_used=tokens_used
                )

                self.report_data_service.update_module_status(
                    module.id, ModuleStatus.COMPLETED, completed_at=datetime.utcnow()
                )

        except Exception as e:
            logger.error(f"执行模块失败: {str(e)}")

            # 更新模块状态为失败
            if module and self.report_data_service:
                self.report_data_service.update_module_status(
                    module.id, ModuleStatus.FAILED,
                    error_message=str(e),
                    error_details={"error_type": type(e).__name__}
                )

            yield (f"❌ 模块执行失败: {str(e)}\n", {
                "type": "module_error",
                "error": str(e)
            })

    def _generate_direct_output(self, processed_data: Dict[str, Any], module_config: Dict[str, Any]) -> str:
        """生成直接输出内容"""
        try:
            # 直接将处理后的数据转换为可读格式
            content = f"**{module_config['module_name']}**\n\n"

            if processed_data.get('summary'):
                content += "### 数据摘要\n\n"
                for key, value in processed_data['summary'].items():
                    if isinstance(value, dict):
                        content += f"**{key}**:\n"
                        for sub_key, sub_value in value.items():
                            content += f"- {sub_key}: {sub_value}\n"
                    else:
                        content += f"- {key}: {value}\n"
                content += "\n"

            if processed_data.get('trends'):
                content += "### 趋势分析\n\n"
                trends = processed_data['trends']
                if isinstance(trends, dict):
                    for trend_type, trend_data in trends.items():
                        content += f"**{trend_type}**: {len(trend_data) if isinstance(trend_data, list) else trend_data}个数据点\n"
                content += "\n"

            return content

        except Exception as e:
            logger.error(f"生成直接输出失败: {str(e)}")
            return f"数据处理完成，但格式化输出时出现错误: {str(e)}"

    def _render_template(self, processed_data: Dict[str, Any], module_config: Dict[str, Any]) -> str:
        """渲染模板内容"""
        try:
            template_content = module_config.get('template_content', '')
            if not template_content:
                return self._generate_direct_output(processed_data, module_config)

            template = Template(template_content)
            return template.render(
                data=processed_data,
                config=module_config,
                module_name=module_config['module_name']
            )

        except Exception as e:
            logger.error(f"模板渲染失败: {str(e)}")
            return f"模板渲染失败: {str(e)}"

    def _generate_chart_config(self, processed_data: Dict[str, Any], module_config: Dict[str, Any]) -> tuple:
        """生成图表配置"""
        try:
            chart_config = {
                "type": "line",
                "title": module_config['module_name'],
                "data": [],
                "options": {}
            }

            # 根据模块类型生成不同的图表配置
            module_type = module_config['module_type']

            if module_type == ModuleType.BASIC_USAGE.value:
                # 基础使用数据 - 生成时间趋势图
                trends = processed_data.get('trends', {}).get('hourly', [])
                if trends:
                    chart_config.update({
                        "type": "line",
                        "data": {
                            "labels": [t.get('hour', '') for t in trends],
                            "datasets": [{
                                "label": "会话数量",
                                "data": [t.get('sessions', 0) for t in trends],
                                "borderColor": "rgb(75, 192, 192)",
                                "tension": 0.1
                            }]
                        }
                    })

            elif module_type == ModuleType.EVENT_ANALYSIS.value:
                # 事件分析 - 生成事件分布图
                events = processed_data.get('events', {})
                if events:
                    chart_config.update({
                        "type": "bar",
                        "data": {
                            "labels": list(events.keys()),
                            "datasets": [{
                                "label": "事件次数",
                                "data": [event.get('count', 0) for event in events.values()],
                                "backgroundColor": "rgba(54, 162, 235, 0.2)",
                                "borderColor": "rgba(54, 162, 235, 1)",
                                "borderWidth": 1
                            }]
                        }
                    })

            content = f"**{module_config['module_name']}**\n\n"
            content += "📈 图表数据已生成，请查看图表展示。\n\n"

            # 添加数据摘要
            if processed_data.get('summary'):
                content += "### 数据摘要\n\n"
                summary = processed_data['summary']
                for key, value in summary.items():
                    content += f"- {key}: {value}\n"

            return content, chart_config

        except Exception as e:
            logger.error(f"生成图表配置失败: {str(e)}")
            return f"图表生成失败: {str(e)}", {}

    async def _generate_llm_content(
        self,
        processed_data: Dict[str, Any],
        module_config: Dict[str, Any],
        model_name: Optional[str] = None
    ) -> tuple:
        """使用LLM生成内容"""
        try:
            llm_service = self._get_llm_service(model_name)

            # 构建系统提示词
            system_prompt = module_config.get('system_prompt', '')
            if not system_prompt:
                system_prompt = self._get_default_system_prompt(module_config['module_type'])

            # 构建用户提示词
            user_prompt = module_config.get('user_prompt', '')
            if not user_prompt:
                user_prompt = self._get_default_user_prompt(module_config['module_type'])

            # 替换提示词变量
            prompt_variables = module_config.get('prompt_variables', {})
            prompt_variables.update({
                'module_name': module_config['module_name'],
                'data': json.dumps(processed_data, ensure_ascii=False, indent=2),
                'target_date': module_config.get('target_date', ''),
                'device_id': module_config.get('device_id', '')
            })

            for var, value in prompt_variables.items():
                system_prompt = system_prompt.replace(f'{{{var}}}', str(value))
                user_prompt = user_prompt.replace(f'{{{var}}}', str(value))

            # 调用LLM生成内容
            response = await llm_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            )

            content = response.get('content', '')
            tokens_used = response.get('tokens_used', 0)

            return content, tokens_used

        except Exception as e:
            logger.error(f"LLM生成内容失败: {str(e)}")
            return f"AI生成失败: {str(e)}", 0

    def _get_default_system_prompt(self, module_type: str) -> str:
        """获取默认系统提示词"""
        prompts = {
            ModuleType.BASIC_USAGE.value: "你是一个专业的数据分析师，擅长分析设备使用数据。请根据提供的数据生成简洁明了的基础使用报告。",
            ModuleType.EVENT_ANALYSIS.value: "你是一个专业的事件分析师，擅长识别重要事件和异常情况。请分析数据中的亮点事件和预警事件。",
            ModuleType.SERVICE_QUALITY.value: "你是一个专业的服务质量分析师，擅长评估服务质量。请分析满意和不满意的服务案例。",
            ModuleType.INSIGHT_ANALYSIS.value: "你是一个专业的洞察分析师，擅长发现数据中的深层模式和趋势。请提供新颖的观察视角。",
            ModuleType.BUSINESS_ADVICE.value: "你是一个专业的业务顾问，擅长基于数据提供实用的经营建议。请提供可操作的业务建议。"
        }
        return prompts.get(module_type, "你是一个专业的数据分析师，请分析提供的数据并生成报告。")

    def _get_default_user_prompt(self, module_type: str) -> str:
        """获取默认用户提示词"""
        return f"请分析以下数据并生成{module_type}相关的报告内容：\n\n{{data}}\n\n请用中文回答，内容要简洁明了，重点突出。"


# 创建单例实例
device_report_service = DeviceReportService()