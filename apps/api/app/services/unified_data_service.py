# -*- coding: utf-8 -*-
"""
统一数据源查询服务 - 支持多种数据源的统一接口
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, asc
import json
from abc import ABC, abstractmethod

from app.database import get_agentos_db, get_speech_ai_robot_db
from app.models.conversation import AosConversationSession, AosConversationMessage
from app.models.statistics import (
    AosStatSessionBehaviorHourly,
    AosStatMessageBehaviorHourly,
    AosStatActionBehaviorHourly,
    AosStatEventBehaviorHourly,
    AosStatUserPreferenceDetailHourly,
    AosStatSessionDurationDistributionHourly,
    AosStatSessionIntervalDistributionHourly,
    AosStatActiveHoursHourly,
    AosStatUserQuestionTopNHourly
)
from app.models.report_models import DataSourceType
from app.services.mysql.position_service import get_device_query_stats_by_day
from app.services.human_stats_service import HumanStatsService

logger = logging.getLogger(__name__)


class BaseDataSource(ABC):
    """数据源基类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    @abstractmethod
    def fetch_data(self, enterprise_id: str, device_id: str, start_time: datetime, 
                   end_time: datetime, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据的抽象方法"""
        pass
    
    @abstractmethod
    def get_data_schema(self) -> Dict[str, Any]:
        """获取数据结构的抽象方法"""
        pass


class StatisticsDataSource(BaseDataSource):
    """统计数据源"""
    
    def fetch_data(self, enterprise_id: str, device_id: str, start_time: datetime, 
                   end_time: datetime, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取统计数据"""
        try:
            logger.info(f"获取统计数据: {enterprise_id}/{device_id}, {start_time} - {end_time}")
            
            # 获取会话行为统计
            session_stats = self._get_session_behavior_stats(enterprise_id, device_id, start_time, end_time)
            
            # 获取消息行为统计
            message_stats = self._get_message_behavior_stats(enterprise_id, device_id, start_time, end_time)
            
            # 获取动作行为统计
            action_stats = self._get_action_behavior_stats(enterprise_id, device_id, start_time, end_time)
            
            # 获取事件行为统计
            event_stats = self._get_event_behavior_stats(enterprise_id, device_id, start_time, end_time)
            
            # 获取时长分布统计
            duration_stats = self._get_duration_distribution_stats(enterprise_id, device_id, start_time, end_time)
            
            # 获取活跃小时统计
            active_hours_stats = self._get_active_hours_stats(enterprise_id, device_id, start_time, end_time)
            
            return {
                "data_source": "statistics",
                "time_range": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                },
                "session_behavior": session_stats,
                "message_behavior": message_stats,
                "action_behavior": action_stats,
                "event_behavior": event_stats,
                "duration_distribution": duration_stats,
                "active_hours": active_hours_stats,
                "summary": {
                    "total_sessions": sum([s.get('session_total', 0) for s in session_stats]),
                    "total_messages": sum([m.get('message_total', 0) for m in message_stats]),
                    "total_actions": sum([a.get('action_count', 0) for a in action_stats]),
                    "data_points": len(session_stats)
                }
            }
            
        except Exception as e:
            logger.error(f"获取统计数据失败: {str(e)}")
            raise
    
    def _get_session_behavior_stats(self, enterprise_id: str, device_id: str, 
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取会话行为统计"""
        results = self.db.query(AosStatSessionBehaviorHourly).filter(
            AosStatSessionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatSessionBehaviorHourly.device_id == device_id,
            AosStatSessionBehaviorHourly.hour_bucket >= start_time,
            AosStatSessionBehaviorHourly.hour_bucket < end_time
        ).order_by(AosStatSessionBehaviorHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "session_total": r.session_total,
                "valid_sessions": r.valid_sessions,
                "no_response_sessions": r.no_response_sessions,
                "avg_conversation_turns": r.avg_conversation_turns,
                "avg_session_duration": r.avg_session_duration,
                "first_assistant_sessions": r.first_assistant_sessions,
                "greeting_repeat_sessions": r.greeting_repeat_sessions,
                "assistant_repeat_content_sessions": r.assistant_repeat_content_sessions
            }
            for r in results
        ]
    
    def _get_message_behavior_stats(self, enterprise_id: str, device_id: str, 
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取消息行为统计"""
        results = self.db.query(AosStatMessageBehaviorHourly).filter(
            AosStatMessageBehaviorHourly.enterprise_id == enterprise_id,
            AosStatMessageBehaviorHourly.device_id == device_id,
            AosStatMessageBehaviorHourly.hour_bucket >= start_time,
            AosStatMessageBehaviorHourly.hour_bucket < end_time
        ).order_by(AosStatMessageBehaviorHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "message_total": r.message_total,
                "user_messages": r.user_messages,
                "assistant_messages": r.assistant_messages,
                "avg_user_msg_length": r.avg_user_msg_length,
                "avg_assistant_msg_length": r.avg_assistant_msg_length,
                "action_trigger_count": r.action_trigger_count,
                "event_trigger_count": r.event_trigger_count
            }
            for r in results
        ]
    
    def _get_action_behavior_stats(self, enterprise_id: str, device_id: str, 
                                  start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取动作行为统计"""
        results = self.db.query(AosStatActionBehaviorHourly).filter(
            AosStatActionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatActionBehaviorHourly.device_id == device_id,
            AosStatActionBehaviorHourly.hour_bucket >= start_time,
            AosStatActionBehaviorHourly.hour_bucket < end_time
        ).order_by(AosStatActionBehaviorHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "action_name": r.action_name,
                "action_count": r.action_count,
                "unique_sessions": r.unique_sessions,
                "avg_execution_time": r.avg_execution_time,
                "success_rate": r.success_rate
            }
            for r in results
        ]
    
    def _get_event_behavior_stats(self, enterprise_id: str, device_id: str, 
                                 start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取事件行为统计"""
        results = self.db.query(AosStatEventBehaviorHourly).filter(
            AosStatEventBehaviorHourly.enterprise_id == enterprise_id,
            AosStatEventBehaviorHourly.device_id == device_id,
            AosStatEventBehaviorHourly.hour_bucket >= start_time,
            AosStatEventBehaviorHourly.hour_bucket < end_time
        ).order_by(AosStatEventBehaviorHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "event_name": r.event_name,
                "event_count": r.event_count,
                "unique_sessions": r.unique_sessions,
                "avg_response_time": r.avg_response_time,
                "success_rate": r.success_rate
            }
            for r in results
        ]
    
    def _get_duration_distribution_stats(self, enterprise_id: str, device_id: str, 
                                        start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取时长分布统计"""
        results = self.db.query(AosStatSessionDurationDistributionHourly).filter(
            AosStatSessionDurationDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionDurationDistributionHourly.device_id == device_id,
            AosStatSessionDurationDistributionHourly.hour_bucket >= start_time,
            AosStatSessionDurationDistributionHourly.hour_bucket < end_time
        ).order_by(AosStatSessionDurationDistributionHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "bucket_lt_30s": r.bucket_lt_30s,
                "bucket_30_60s": r.bucket_30_60s,
                "bucket_1_3min": r.bucket_1_3min,
                "bucket_3_5min": r.bucket_3_5min,
                "bucket_5_10min": r.bucket_5_10min,
                "bucket_10_20min": r.bucket_10_20min,
                "bucket_gt_20min": r.bucket_gt_20min
            }
            for r in results
        ]
    
    def _get_active_hours_stats(self, enterprise_id: str, device_id: str, 
                               start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取活跃小时统计"""
        results = self.db.query(AosStatActiveHoursHourly).filter(
            AosStatActiveHoursHourly.enterprise_id == enterprise_id,
            AosStatActiveHoursHourly.device_id == device_id,
            AosStatActiveHoursHourly.hour_bucket >= start_time,
            AosStatActiveHoursHourly.hour_bucket < end_time
        ).order_by(AosStatActiveHoursHourly.hour_bucket).all()
        
        return [
            {
                "hour_bucket": r.hour_bucket.isoformat(),
                "consecutive_active_hours": r.consecutive_active_hours,
                "assistant_first_ratio": r.assistant_first_ratio,
                "greeting_ratio": r.greeting_ratio
            }
            for r in results
        ]
    
    def get_data_schema(self) -> Dict[str, Any]:
        """获取统计数据结构"""
        return {
            "data_source": "statistics",
            "description": "从统计表获取聚合数据",
            "tables": [
                "aos_stat_session_behavior_hourly",
                "aos_stat_message_behavior_hourly", 
                "aos_stat_action_behavior_hourly",
                "aos_stat_event_behavior_hourly",
                "aos_stat_session_duration_distribution_hourly",
                "aos_stat_active_hours_hourly"
            ],
            "time_granularity": "hourly",
            "metrics": [
                "session_behavior", "message_behavior", "action_behavior",
                "event_behavior", "duration_distribution", "active_hours"
            ]
        }


class ConversationDataSource(BaseDataSource):
    """会话数据源"""
    
    def fetch_data(self, enterprise_id: str, device_id: str, start_time: datetime, 
                   end_time: datetime, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取会话数据"""
        try:
            logger.info(f"获取会话数据: {enterprise_id}/{device_id}, {start_time} - {end_time}")
            
            # 获取会话列表
            sessions = self._get_sessions(enterprise_id, device_id, start_time, end_time, config)
            
            # 获取消息详情
            if config.get('include_messages', True) and sessions:
                session_ids = [s['session_id'] for s in sessions]
                messages = self._get_messages(session_ids, config)
            else:
                messages = []
            
            return {
                "data_source": "conversation",
                "time_range": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                },
                "sessions": sessions,
                "messages": messages,
                "summary": {
                    "total_sessions": len(sessions),
                    "total_messages": len(messages),
                    "avg_messages_per_session": len(messages) / len(sessions) if sessions else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取会话数据失败: {str(e)}")
            raise
    
    def _get_sessions(self, enterprise_id: str, device_id: str, start_time: datetime, 
                     end_time: datetime, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取会话列表"""
        query = self.db.query(AosConversationSession).filter(
            AosConversationSession.enterprise_id == enterprise_id,
            AosConversationSession.device_id == device_id,
            AosConversationSession.created_at >= start_time,
            AosConversationSession.created_at < end_time
        )
        
        # 应用过滤条件
        if config.get('filter_status'):
            query = query.filter(AosConversationSession.status == config['filter_status'])
        
        # 限制记录数
        max_records = config.get('max_records', 1000)
        results = query.order_by(desc(AosConversationSession.created_at)).limit(max_records).all()
        
        return [
            {
                "session_id": s.session_id,
                "enterprise_id": s.enterprise_id,
                "device_id": s.device_id,
                "status": s.status,
                "created_at": s.created_at.isoformat(),
                "updated_at": s.updated_at.isoformat() if s.updated_at else None,
                "conversation_turns": s.conversation_turns,
                "session_duration": s.session_duration,
                "first_message_role": s.first_message_role,
                "last_message_role": s.last_message_role
            }
            for s in results
        ]
    
    def _get_messages(self, session_ids: List[str], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取消息列表"""
        if not session_ids:
            return []
        
        query = self.db.query(AosConversationMessage).filter(
            AosConversationMessage.session_id.in_(session_ids)
        )
        
        results = query.order_by(
            AosConversationMessage.session_id,
            AosConversationMessage.message_index
        ).all()
        
        return [
            {
                "message_id": m.message_id,
                "session_id": m.session_id,
                "message_index": m.message_index,
                "role": m.role,
                "content": m.content,
                "created_at": m.created_at.isoformat(),
                "action_name": m.action_name,
                "action_input": m.action_input,
                "action_output": m.action_output,
                "event_name": m.event_name,
                "event_data": m.event_data
            }
            for m in results
        ]
    
    def get_data_schema(self) -> Dict[str, Any]:
        """获取会话数据结构"""
        return {
            "data_source": "conversation",
            "description": "从会话表获取原始对话数据",
            "tables": [
                "aos_conversation_sessions",
                "aos_conversation_messages"
            ],
            "time_granularity": "message_level",
            "fields": [
                "session_id", "enterprise_id", "device_id", "status",
                "conversation_turns", "session_duration", "messages"
            ]
        }


class QueryDataSource(BaseDataSource):
    """查询数据源"""

    def fetch_data(self, enterprise_id: str, device_id: str, start_time: datetime,
                   end_time: datetime, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取查询数据"""
        try:
            logger.info(f"获取查询数据: {enterprise_id}/{device_id}, {start_time} - {end_time}")

            # 确定数据库连接
            scope = config.get('scope', 'cn')
            db_key = "bigdata_cn" if scope == "cn" else "default"

            # 获取查询统计数据
            start_date = start_time.strftime('%Y-%m-%d')
            query_stats = get_device_query_stats_by_day(
                device_ids=[device_id],
                start_date=start_date,
                db_key=db_key
            )

            return {
                "data_source": "query",
                "time_range": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                },
                "query_stats": query_stats,
                "summary": {
                    "total_queries": sum([q.get('query_count', 0) for q in query_stats]),
                    "unique_days": len(set([q.get('date') for q in query_stats])),
                    "data_points": len(query_stats)
                }
            }

        except Exception as e:
            logger.error(f"获取查询数据失败: {str(e)}")
            raise

    def get_data_schema(self) -> Dict[str, Any]:
        """获取查询数据结构"""
        return {
            "data_source": "query",
            "description": "从查询日志获取用户查询数据",
            "tables": ["query_logs"],
            "time_granularity": "daily",
            "fields": [
                "enterprise_id", "enterprise_name", "device_id",
                "date", "query_count"
            ]
        }


class HumanStatsDataSource(BaseDataSource):
    """人脸识别数据源"""

    def __init__(self, db: Session):
        super().__init__(db)
        self.human_stats_service = HumanStatsService(db)

    def fetch_data(self, enterprise_id: str, device_id: str, start_time: datetime,
                   end_time: datetime, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取人脸识别数据"""
        try:
            logger.info(f"获取人脸识别数据: {enterprise_id}/{device_id}, {start_time} - {end_time}")

            # 获取人脸识别统计数据
            group_by = config.get('group_by', 'day')
            human_stats = self.human_stats_service.get_human_stats_grouped(
                enterprise_id=enterprise_id,
                device_id=device_id,
                start_time=start_time,
                end_time=end_time,
                group_by=group_by
            )

            return {
                "data_source": "human_stats",
                "time_range": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                },
                "human_stats": human_stats,
                "summary": {
                    "total_records": sum([
                        h.get('overall_summary', {}).get('total_records', 0)
                        for h in human_stats.get('data', [])
                    ]),
                    "unique_visitors": sum([
                        len(h.get('recognized_visitors_summary', []))
                        for h in human_stats.get('data', [])
                    ]),
                    "data_points": len(human_stats.get('data', []))
                }
            }

        except Exception as e:
            logger.error(f"获取人脸识别数据失败: {str(e)}")
            raise

    def get_data_schema(self) -> Dict[str, Any]:
        """获取人脸识别数据结构"""
        return {
            "data_source": "human_stats",
            "description": "从人脸识别系统获取访客数据",
            "tables": ["human_detection_*"],
            "time_granularity": "configurable",
            "fields": [
                "overall_summary", "recognition_performance_metrics",
                "gender_distribution_metrics", "age_distribution_metrics",
                "recognized_visitors_summary", "records_by_hour_of_day"
            ]
        }


class UnifiedDataService:
    """统一数据服务"""

    def __init__(self, agentos_db: Session, speech_ai_robot_db: Session):
        """
        初始化统一数据服务

        Args:
            agentos_db: agentos数据库会话
            speech_ai_robot_db: speech_ai_robot数据库会话
        """
        self.agentos_db = agentos_db
        self.speech_ai_robot_db = speech_ai_robot_db

        # 初始化各种数据源
        self.data_sources = {
            DataSourceType.STATISTICS.value: StatisticsDataSource(speech_ai_robot_db),
            DataSourceType.CONVERSATION.value: ConversationDataSource(agentos_db),
            DataSourceType.QUERY.value: QueryDataSource(agentos_db),
            DataSourceType.HUMAN_STATS.value: HumanStatsDataSource(agentos_db)
        }

    def fetch_data(self, data_source_type: str, enterprise_id: str, device_id: str,
                   start_time: datetime, end_time: datetime,
                   config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从指定数据源获取数据

        Args:
            data_source_type: 数据源类型
            enterprise_id: 企业ID
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间
            config: 数据源配置

        Returns:
            数据结果
        """
        if config is None:
            config = {}

        if data_source_type not in self.data_sources:
            raise ValueError(f"不支持的数据源类型: {data_source_type}")

        data_source = self.data_sources[data_source_type]
        return data_source.fetch_data(enterprise_id, device_id, start_time, end_time, config)

    def fetch_mixed_data(self, data_source_types: List[str], enterprise_id: str,
                        device_id: str, start_time: datetime, end_time: datetime,
                        configs: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        从多个数据源获取混合数据

        Args:
            data_source_types: 数据源类型列表
            enterprise_id: 企业ID
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间
            configs: 各数据源的配置

        Returns:
            混合数据结果
        """
        if configs is None:
            configs = {}

        mixed_data = {
            "data_source": "mixed",
            "time_range": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            },
            "sources": {},
            "summary": {
                "total_sources": len(data_source_types),
                "successful_sources": 0,
                "failed_sources": []
            }
        }

        for source_type in data_source_types:
            try:
                source_config = configs.get(source_type, {})
                data = self.fetch_data(source_type, enterprise_id, device_id,
                                     start_time, end_time, source_config)
                mixed_data["sources"][source_type] = data
                mixed_data["summary"]["successful_sources"] += 1

            except Exception as e:
                logger.error(f"获取数据源 {source_type} 数据失败: {str(e)}")
                mixed_data["summary"]["failed_sources"].append({
                    "source_type": source_type,
                    "error": str(e)
                })

        return mixed_data

    def get_available_data_sources(self) -> List[Dict[str, Any]]:
        """获取可用的数据源列表"""
        return [
            data_source.get_data_schema()
            for data_source in self.data_sources.values()
        ]

    def validate_data_source_config(self, data_source_type: str,
                                   config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证数据源配置

        Args:
            data_source_type: 数据源类型
            config: 配置信息

        Returns:
            验证结果
        """
        if data_source_type not in self.data_sources:
            return {
                "valid": False,
                "error": f"不支持的数据源类型: {data_source_type}"
            }

        # 这里可以添加更详细的配置验证逻辑
        return {
            "valid": True,
            "data_source_type": data_source_type,
            "schema": self.data_sources[data_source_type].get_data_schema()
        }
