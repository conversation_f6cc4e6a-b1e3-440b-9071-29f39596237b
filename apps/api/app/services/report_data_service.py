# -*- coding: utf-8 -*-
"""
报告数据服务 - 处理报告生成记录的数据库操作（简化版）
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.models.report_models import (
    DeviceReport, ReportModule, ModuleTemplate, DataSourceConfig,
    ReportStatus, ReportType, PromptType, ModuleType, DataSourceType,
    ExecutionMode, ModuleStatus
)

logger = logging.getLogger(__name__)


class ReportDataService:
    """报告数据服务类"""
    
    def __init__(self, db: Session):
        """
        初始化报告数据服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def create_report_record(
        self,
        enterprise_id: str,
        device_id: str,
        report_type: ReportType,
        target_date: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        prompt_version: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        prompt_type: PromptType = PromptType.SYSTEM,
        model_name: Optional[str] = None
    ) -> DeviceReport:
        """
        创建报告生成记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            report_type: 报告类型
            target_date: 目标日期
            start_date: 开始日期(多天报告)
            end_date: 结束日期(多天报告)
            prompt_version: Prompt版本
            custom_system_prompt: 自定义系统Prompt
            custom_user_prompt: 自定义用户Prompt
            prompt_type: Prompt类型
            
        Returns:
            创建的报告记录
        """
        try:
            report = DeviceReport(
                enterprise_id=enterprise_id,
                device_id=device_id,
                report_type=report_type.value,
                status=ReportStatus.GENERATING.value,
                target_date=target_date,
                start_date=start_date,
                end_date=end_date,
                prompt_version=prompt_version,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                prompt_type=prompt_type.value,
                model_name=model_name,
                generation_start_time=datetime.utcnow()
            )
            
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"创建报告记录成功: ID={report.id}, 企业={enterprise_id}, 设备={device_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建报告记录失败: {str(e)}")
            raise
    
    def update_report_completion(
        self,
        report_id: int,
        system_prompt_used: str,
        user_prompt_used: str,
        generated_content: str,
        model_name: str,
        tokens_used: Optional[int] = None,
        chunk_count: int = 0,
        prompt_type: Optional[PromptType] = None
    ) -> DeviceReport:
        """
        更新报告完成信息
        
        Args:
            report_id: 报告ID
            system_prompt_used: 实际使用的系统Prompt
            user_prompt_used: 实际使用的用户Prompt
            generated_content: 生成的报告内容
            model_name: 使用的模型名称
            tokens_used: 使用的Token数量
            chunk_count: 内容块数量
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.status = ReportStatus.COMPLETED.value
            report.generation_end_time = datetime.utcnow()
            report.system_prompt_used = system_prompt_used
            report.user_prompt_used = user_prompt_used
            report.generated_content = generated_content
            report.model_name = model_name
            report.content_length = len(generated_content)
            report.chunk_count = chunk_count
            report.tokens_used = tokens_used
            
            # 更新Prompt类型（如果提供）
            if prompt_type is not None:
                report.prompt_type = prompt_type.value
                logger.info(f"更新Prompt类型: {prompt_type.value}")
            
            # 计算生成耗时
            if report.generation_start_time and report.generation_end_time:
                duration = (report.generation_end_time - report.generation_start_time).total_seconds()
                report.generation_duration = int(duration)
            
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"更新报告完成信息成功: ID={report_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新报告完成信息失败: {str(e)}")
            raise
    
    def update_report_failure(
        self,
        report_id: int,
        error_message: str
    ) -> DeviceReport:
        """
        更新报告失败信息
        
        Args:
            report_id: 报告ID
            error_message: 错误信息
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.status = ReportStatus.FAILED.value
            report.generation_end_time = datetime.utcnow()
            report.error_message = error_message
            
            # 计算生成耗时
            if report.generation_start_time and report.generation_end_time:
                duration = (report.generation_end_time - report.generation_start_time).total_seconds()
                report.generation_duration = int(duration)
            
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"更新报告失败信息成功: ID={report_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新报告失败信息失败: {str(e)}")
            raise
    
    def get_report_by_id(self, report_id: int) -> Optional[DeviceReport]:
        """
        根据ID获取报告记录
        
        Args:
            report_id: 报告ID
            
        Returns:
            报告记录
        """
        return self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
    
    def get_reports_by_device(
        self,
        enterprise_id: str,
        device_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        获取设备的报告记录列表
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            and_(
                DeviceReport.enterprise_id == enterprise_id,
                DeviceReport.device_id == device_id
            )
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()

    # ==================== 模块化报告相关方法 ====================

    def create_modular_report(
        self,
        enterprise_id: str,
        device_id: str,
        target_date: str,
        module_configs: List[Dict[str, Any]],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> DeviceReport:
        """
        创建模块化报告记录

        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            target_date: 目标日期
            module_configs: 模块配置列表
            start_date: 开始日期(多天报告)
            end_date: 结束日期(多天报告)

        Returns:
            创建的报告记录
        """
        try:
            # 创建主报告记录
            report = DeviceReport(
                enterprise_id=enterprise_id,
                device_id=device_id,
                report_type=ReportType.MODULAR.value,
                status=ReportStatus.GENERATING.value,
                target_date=target_date,
                start_date=start_date,
                end_date=end_date,
                is_modular=True,
                module_config={"modules": module_configs},
                total_modules=len(module_configs),
                completed_modules=0,
                generation_start_time=datetime.utcnow()
            )

            self.db.add(report)
            self.db.flush()  # 获取report.id

            # 创建模块记录
            for i, module_config in enumerate(module_configs):
                module = ReportModule(
                    report_id=report.id,
                    module_type=module_config['module_type'],
                    module_name=module_config['module_name'],
                    module_description=module_config.get('module_description', ''),
                    execution_order=i + 1,
                    status=ModuleStatus.PENDING.value,
                    data_sources=module_config.get('data_sources', {}),
                    primary_data_source=module_config.get('primary_data_source'),
                    execution_mode=module_config.get('execution_mode', ExecutionMode.LLM_GENERATE.value),
                    use_llm=module_config.get('use_llm', True),
                    template_name=module_config.get('template_name'),
                    custom_config=module_config.get('custom_config', {}),
                    system_prompt=module_config.get('system_prompt'),
                    user_prompt=module_config.get('user_prompt'),
                    prompt_variables=module_config.get('prompt_variables', {})
                )
                self.db.add(module)

            self.db.commit()
            self.db.refresh(report)

            logger.info(f"创建模块化报告成功: ID={report.id}, 模块数={len(module_configs)}")
            return report

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建模块化报告失败: {str(e)}")
            raise

    def get_report_modules(self, report_id: int) -> List[ReportModule]:
        """
        获取报告的模块列表

        Args:
            report_id: 报告ID

        Returns:
            模块列表
        """
        return self.db.query(ReportModule).filter(
            ReportModule.report_id == report_id
        ).order_by(ReportModule.execution_order).all()

    def update_module_status(
        self,
        module_id: int,
        status: ModuleStatus,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None,
        error_message: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> ReportModule:
        """
        更新模块状态

        Args:
            module_id: 模块ID
            status: 新状态
            started_at: 开始时间
            completed_at: 完成时间
            error_message: 错误信息
            error_details: 错误详情

        Returns:
            更新后的模块记录
        """
        try:
            module = self.db.query(ReportModule).filter(ReportModule.id == module_id).first()
            if not module:
                raise ValueError(f"模块记录不存在: ID={module_id}")

            module.status = status.value
            if started_at:
                module.started_at = started_at
            if completed_at:
                module.completed_at = completed_at
            if error_message:
                module.error_message = error_message
            if error_details:
                module.error_details = error_details

            self.db.commit()
            self.db.refresh(module)

            # 更新主报告的完成模块数
            if status == ModuleStatus.COMPLETED:
                self._update_report_progress(module.report_id)

            logger.info(f"更新模块状态成功: ID={module_id}, 状态={status.value}")
            return module

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新模块状态失败: {str(e)}")
            raise

    def update_module_result(
        self,
        module_id: int,
        raw_data: Optional[Dict[str, Any]] = None,
        processed_data: Optional[Dict[str, Any]] = None,
        generated_content: Optional[str] = None,
        chart_config: Optional[Dict[str, Any]] = None,
        interactive_data: Optional[Dict[str, Any]] = None,
        performance_metrics: Optional[Dict[str, int]] = None,
        tokens_used: Optional[int] = None
    ) -> ReportModule:
        """
        更新模块执行结果

        Args:
            module_id: 模块ID
            raw_data: 原始数据
            processed_data: 处理后数据
            generated_content: 生成的内容
            chart_config: 图表配置
            interactive_data: 交互数据
            performance_metrics: 性能指标
            tokens_used: 使用的Token数量

        Returns:
            更新后的模块记录
        """
        try:
            module = self.db.query(ReportModule).filter(ReportModule.id == module_id).first()
            if not module:
                raise ValueError(f"模块记录不存在: ID={module_id}")

            if raw_data is not None:
                module.raw_data = raw_data
            if processed_data is not None:
                module.processed_data = processed_data
            if generated_content is not None:
                module.generated_content = generated_content
            if chart_config is not None:
                module.chart_config = chart_config
            if interactive_data is not None:
                module.interactive_data = interactive_data
            if tokens_used is not None:
                module.tokens_used = tokens_used

            # 更新性能指标
            if performance_metrics:
                if 'data_fetch_duration' in performance_metrics:
                    module.data_fetch_duration = performance_metrics['data_fetch_duration']
                if 'processing_duration' in performance_metrics:
                    module.processing_duration = performance_metrics['processing_duration']
                if 'generation_duration' in performance_metrics:
                    module.generation_duration = performance_metrics['generation_duration']
                if 'total_duration' in performance_metrics:
                    module.total_duration = performance_metrics['total_duration']

            self.db.commit()
            self.db.refresh(module)

            logger.info(f"更新模块结果成功: ID={module_id}")
            return module

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新模块结果失败: {str(e)}")
            raise

    def _update_report_progress(self, report_id: int):
        """更新报告进度"""
        try:
            # 统计已完成的模块数
            completed_count = self.db.query(ReportModule).filter(
                ReportModule.report_id == report_id,
                ReportModule.status == ModuleStatus.COMPLETED.value
            ).count()

            # 更新主报告
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if report:
                report.completed_modules = completed_count

                # 如果所有模块都完成了，更新报告状态
                if completed_count >= report.total_modules:
                    report.status = ReportStatus.COMPLETED.value
                    report.generation_end_time = datetime.utcnow()

                self.db.commit()
                logger.info(f"更新报告进度: ID={report_id}, 完成={completed_count}/{report.total_modules}")

        except Exception as e:
            logger.error(f"更新报告进度失败: {str(e)}")

    # ==================== 模板管理相关方法 ====================

    def get_module_templates(self, module_type: Optional[str] = None,
                           is_active: bool = True) -> List[ModuleTemplate]:
        """
        获取模块模板列表

        Args:
            module_type: 模块类型过滤
            is_active: 是否只获取启用的模板

        Returns:
            模板列表
        """
        query = self.db.query(ModuleTemplate)

        if module_type:
            query = query.filter(ModuleTemplate.module_type == module_type)
        if is_active:
            query = query.filter(ModuleTemplate.is_active == True)

        return query.order_by(ModuleTemplate.module_type, ModuleTemplate.template_name).all()

    def get_template_by_name(self, template_name: str) -> Optional[ModuleTemplate]:
        """
        根据名称获取模板

        Args:
            template_name: 模板名称

        Returns:
            模板记录
        """
        return self.db.query(ModuleTemplate).filter(
            ModuleTemplate.template_name == template_name,
            ModuleTemplate.is_active == True
        ).first()

    # ==================== 数据源配置相关方法 ====================

    def get_data_source_configs(self, data_source_type: Optional[str] = None,
                               is_active: bool = True) -> List[DataSourceConfig]:
        """
        获取数据源配置列表

        Args:
            data_source_type: 数据源类型过滤
            is_active: 是否只获取启用的配置

        Returns:
            配置列表
        """
        query = self.db.query(DataSourceConfig)

        if data_source_type:
            query = query.filter(DataSourceConfig.data_source_type == data_source_type)
        if is_active:
            query = query.filter(DataSourceConfig.is_active == True)

        return query.order_by(DataSourceConfig.data_source_type, DataSourceConfig.config_name).all()

    def get_data_source_config_by_name(self, config_name: str) -> Optional[DataSourceConfig]:
        """
        根据名称获取数据源配置

        Args:
            config_name: 配置名称

        Returns:
            配置记录
        """
        return self.db.query(DataSourceConfig).filter(
            DataSourceConfig.config_name == config_name,
            DataSourceConfig.is_active == True
        ).first()
    
    def get_reports_by_date_range(
        self,
        enterprise_id: str,
        device_id: str,
        start_date: str,
        end_date: str
    ) -> List[DeviceReport]:
        """
        根据日期范围获取报告记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            and_(
                DeviceReport.enterprise_id == enterprise_id,
                DeviceReport.device_id == device_id,
                DeviceReport.target_date >= start_date,
                DeviceReport.target_date <= end_date
            )
        ).order_by(desc(DeviceReport.created_at)).all()
    
    def get_reports_by_prompt_version(
        self,
        prompt_version: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        根据Prompt版本获取报告记录
        
        Args:
            prompt_version: Prompt版本
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            DeviceReport.prompt_version == prompt_version
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()
    
    def get_reports_by_prompt_type(
        self,
        prompt_type: PromptType,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        根据Prompt类型获取报告记录
        
        Args:
            prompt_type: Prompt类型
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            DeviceReport.prompt_type == prompt_type
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()
    
    def update_chunk_count(self, report_id: int, chunk_count: int) -> DeviceReport:
        """
        更新内容块数量
        
        Args:
            report_id: 报告ID
            chunk_count: 内容块数量
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.chunk_count = chunk_count
            
            self.db.commit()
            self.db.refresh(report)
            
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新内容块数量失败: {str(e)}")
            raise 