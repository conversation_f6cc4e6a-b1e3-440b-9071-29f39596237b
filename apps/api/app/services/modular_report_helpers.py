# -*- coding: utf-8 -*-
"""
模块化报告辅助方法
包含数据获取、处理、生成等核心功能
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from jinja2 import Template

from app.models.report_models import ModuleType, DataSourceType, ExecutionMode

logger = logging.getLogger(__name__)


class ModularReportHelpers:
    """模块化报告辅助方法类"""
    
    def __init__(self, unified_data_service, llm_service=None):
        """
        初始化辅助方法类
        
        Args:
            unified_data_service: 统一数据服务
            llm_service: LLM服务
        """
        self.unified_data_service = unified_data_service
        self.llm_service = llm_service
    
    async def fetch_module_data(
        self,
        enterprise_id: str,
        device_id: str,
        target_date: str,
        start_date: Optional[str],
        end_date: Optional[str],
        module_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        获取模块数据
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            target_date: 目标日期
            start_date: 开始日期
            end_date: 结束日期
            module_config: 模块配置
            
        Returns:
            原始数据
        """
        try:
            # 确定时间范围
            if start_date and end_date:
                start_time = datetime.strptime(start_date, '%Y-%m-%d')
                end_time = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            else:
                start_time = datetime.strptime(target_date, '%Y-%m-%d')
                end_time = start_time + timedelta(days=1)
            
            # 获取数据源配置
            data_sources = module_config.get('data_sources', {})
            primary_data_source = module_config.get('primary_data_source')
            
            if not primary_data_source:
                raise ValueError("未指定主要数据源")
            
            # 根据数据源类型获取数据
            if primary_data_source == DataSourceType.MIXED.value:
                # 混合数据源
                source_types = list(data_sources.keys())
                configs = {k: v for k, v in data_sources.items() if k != 'mixed'}
                
                raw_data = self.unified_data_service.fetch_mixed_data(
                    data_source_types=source_types,
                    enterprise_id=enterprise_id,
                    device_id=device_id,
                    start_time=start_time,
                    end_time=end_time,
                    configs=configs
                )
            else:
                # 单一数据源
                source_config = data_sources.get(primary_data_source, {})
                
                raw_data = self.unified_data_service.fetch_data(
                    data_source_type=primary_data_source,
                    enterprise_id=enterprise_id,
                    device_id=device_id,
                    start_time=start_time,
                    end_time=end_time,
                    config=source_config
                )
            
            logger.info(f"获取模块数据成功: {primary_data_source}, 数据量: {len(str(raw_data))}")
            return raw_data
            
        except Exception as e:
            logger.error(f"获取模块数据失败: {str(e)}")
            raise
    
    async def process_module_data(
        self,
        raw_data: Dict[str, Any],
        module_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理模块数据
        
        Args:
            raw_data: 原始数据
            module_config: 模块配置
            
        Returns:
            处理后的数据
        """
        try:
            module_type = module_config['module_type']
            
            if module_type == ModuleType.BASIC_USAGE.value:
                return self._process_basic_usage_data(raw_data, module_config)
            elif module_type == ModuleType.EVENT_ANALYSIS.value:
                return self._process_event_analysis_data(raw_data, module_config)
            elif module_type == ModuleType.SERVICE_QUALITY.value:
                return self._process_service_quality_data(raw_data, module_config)
            elif module_type == ModuleType.INSIGHT_ANALYSIS.value:
                return self._process_insight_analysis_data(raw_data, module_config)
            elif module_type == ModuleType.BUSINESS_ADVICE.value:
                return self._process_business_advice_data(raw_data, module_config)
            else:
                # 默认处理：直接返回原始数据的摘要
                return self._process_default_data(raw_data, module_config)
                
        except Exception as e:
            logger.error(f"处理模块数据失败: {str(e)}")
            raise
    
    def _process_basic_usage_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理基础使用数据"""
        processed = {
            "module_type": "basic_usage",
            "summary": {},
            "trends": {},
            "actions": {}
        }
        
        # 处理统计数据
        if 'statistics' in raw_data.get('sources', {}):
            stats = raw_data['sources']['statistics']
            
            # 会话统计
            session_data = stats.get('session_behavior', [])
            if session_data:
                total_sessions = sum([s.get('session_total', 0) for s in session_data])
                valid_sessions = sum([s.get('valid_sessions', 0) for s in session_data])
                avg_duration = sum([s.get('avg_session_duration', 0) for s in session_data]) / len(session_data)
                
                processed['summary']['sessions'] = {
                    "total": total_sessions,
                    "valid": valid_sessions,
                    "avg_duration": round(avg_duration, 2),
                    "valid_rate": round(valid_sessions / total_sessions * 100, 2) if total_sessions > 0 else 0
                }
            
            # 消息统计
            message_data = stats.get('message_behavior', [])
            if message_data:
                total_messages = sum([m.get('message_total', 0) for m in message_data])
                user_messages = sum([m.get('user_messages', 0) for m in message_data])
                assistant_messages = sum([m.get('assistant_messages', 0) for m in message_data])
                
                processed['summary']['messages'] = {
                    "total": total_messages,
                    "user": user_messages,
                    "assistant": assistant_messages,
                    "avg_per_session": round(total_messages / processed['summary']['sessions']['total'], 2) if processed['summary'].get('sessions', {}).get('total', 0) > 0 else 0
                }
            
            # 动作统计
            action_data = stats.get('action_behavior', [])
            if action_data:
                action_summary = {}
                for action in action_data:
                    action_name = action.get('action_name', 'unknown')
                    if action_name not in action_summary:
                        action_summary[action_name] = {
                            "count": 0,
                            "unique_sessions": 0,
                            "success_rate": 0
                        }
                    action_summary[action_name]["count"] += action.get('action_count', 0)
                    action_summary[action_name]["unique_sessions"] += action.get('unique_sessions', 0)
                    action_summary[action_name]["success_rate"] = action.get('success_rate', 0)
                
                processed['actions'] = action_summary
        
        # 处理时间趋势
        if 'statistics' in raw_data.get('sources', {}):
            stats = raw_data['sources']['statistics']
            session_data = stats.get('session_behavior', [])
            
            hourly_trends = []
            for session in session_data:
                hourly_trends.append({
                    "hour": session.get('hour_bucket'),
                    "sessions": session.get('session_total', 0),
                    "valid_sessions": session.get('valid_sessions', 0),
                    "avg_duration": session.get('avg_session_duration', 0)
                })
            
            processed['trends']['hourly'] = hourly_trends
        
        return processed
    
    def _process_event_analysis_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理事件分析数据"""
        processed = {
            "module_type": "event_analysis",
            "highlights": [],
            "warnings": [],
            "events": {}
        }
        
        # 处理事件数据
        if 'statistics' in raw_data.get('sources', {}):
            stats = raw_data['sources']['statistics']
            event_data = stats.get('event_behavior', [])
            
            event_summary = {}
            for event in event_data:
                event_name = event.get('event_name', 'unknown')
                if event_name not in event_summary:
                    event_summary[event_name] = {
                        "count": 0,
                        "unique_sessions": 0,
                        "avg_response_time": 0,
                        "success_rate": 0
                    }
                event_summary[event_name]["count"] += event.get('event_count', 0)
                event_summary[event_name]["unique_sessions"] += event.get('unique_sessions', 0)
                event_summary[event_name]["avg_response_time"] = event.get('avg_response_time', 0)
                event_summary[event_name]["success_rate"] = event.get('success_rate', 0)
            
            processed['events'] = event_summary
            
            # 识别亮点和预警
            for event_name, event_info in event_summary.items():
                if event_info['success_rate'] > 90 and event_info['count'] > 10:
                    processed['highlights'].append({
                        "type": "high_success_rate",
                        "event": event_name,
                        "description": f"{event_name}事件成功率达到{event_info['success_rate']:.1f}%",
                        "count": event_info['count']
                    })
                
                if event_info['success_rate'] < 70 and event_info['count'] > 5:
                    processed['warnings'].append({
                        "type": "low_success_rate",
                        "event": event_name,
                        "description": f"{event_name}事件成功率仅为{event_info['success_rate']:.1f}%",
                        "count": event_info['count']
                    })
        
        return processed
    
    def _process_service_quality_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理服务质量数据"""
        processed = {
            "module_type": "service_quality",
            "satisfactory_cases": [],
            "unsatisfactory_cases": [],
            "quality_metrics": {}
        }
        
        # 处理会话数据，分析服务质量
        if 'conversation' in raw_data.get('sources', {}):
            conv_data = raw_data['sources']['conversation']
            sessions = conv_data.get('sessions', [])
            
            for session in sessions:
                # 根据会话轮数和时长判断服务质量
                turns = session.get('conversation_turns', 0)
                duration = session.get('session_duration', 0)
                
                if turns >= 3 and duration >= 60:  # 满意案例：多轮对话且时长适中
                    processed['satisfactory_cases'].append({
                        "session_id": session.get('session_id'),
                        "turns": turns,
                        "duration": duration,
                        "reason": "多轮有效对话"
                    })
                elif turns <= 1 or duration <= 10:  # 不满意案例：单轮对话或时长过短
                    processed['unsatisfactory_cases'].append({
                        "session_id": session.get('session_id'),
                        "turns": turns,
                        "duration": duration,
                        "reason": "对话轮数过少或时长过短"
                    })
            
            # 计算质量指标
            total_sessions = len(sessions)
            satisfactory_count = len(processed['satisfactory_cases'])
            unsatisfactory_count = len(processed['unsatisfactory_cases'])
            
            processed['quality_metrics'] = {
                "total_sessions": total_sessions,
                "satisfactory_count": satisfactory_count,
                "unsatisfactory_count": unsatisfactory_count,
                "satisfaction_rate": round(satisfactory_count / total_sessions * 100, 2) if total_sessions > 0 else 0
            }
        
        return processed
    
    def _process_insight_analysis_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理洞察分析数据"""
        processed = {
            "module_type": "insight_analysis",
            "insights": [],
            "patterns": {},
            "anomalies": []
        }
        
        # 分析使用模式
        if 'statistics' in raw_data.get('sources', {}):
            stats = raw_data['sources']['statistics']
            
            # 活跃时间分析
            active_hours = stats.get('active_hours', [])
            if active_hours:
                peak_hours = []
                for hour_data in active_hours:
                    consecutive_hours = hour_data.get('consecutive_active_hours', 0)
                    if consecutive_hours >= 3:
                        peak_hours.append(hour_data.get('hour_bucket'))
                
                if peak_hours:
                    processed['insights'].append({
                        "type": "peak_usage_pattern",
                        "description": f"发现{len(peak_hours)}个高活跃时段",
                        "details": peak_hours
                    })
            
            # 时长分布分析
            duration_dist = stats.get('duration_distribution', [])
            if duration_dist:
                short_sessions = sum([d.get('bucket_lt_30s', 0) for d in duration_dist])
                long_sessions = sum([d.get('bucket_gt_20min', 0) for d in duration_dist])
                total_sessions = sum([
                    d.get('bucket_lt_30s', 0) + d.get('bucket_30_60s', 0) + 
                    d.get('bucket_1_3min', 0) + d.get('bucket_3_5min', 0) + 
                    d.get('bucket_5_10min', 0) + d.get('bucket_10_20min', 0) + 
                    d.get('bucket_gt_20min', 0) for d in duration_dist
                ])
                
                if total_sessions > 0:
                    short_rate = short_sessions / total_sessions * 100
                    long_rate = long_sessions / total_sessions * 100
                    
                    if short_rate > 30:
                        processed['anomalies'].append({
                            "type": "high_short_sessions",
                            "description": f"短时会话占比过高({short_rate:.1f}%)",
                            "impact": "可能存在用户体验问题"
                        })
                    
                    if long_rate > 10:
                        processed['insights'].append({
                            "type": "deep_engagement",
                            "description": f"长时会话占比{long_rate:.1f}%，用户参与度较高",
                            "value": "用户对服务满意度较高"
                        })
        
        return processed
    
    def _process_business_advice_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理业务建议数据"""
        processed = {
            "module_type": "business_advice",
            "recommendations": [],
            "opportunities": [],
            "risks": []
        }
        
        # 基于数据分析生成业务建议
        if 'statistics' in raw_data.get('sources', {}):
            stats = raw_data['sources']['statistics']
            summary = stats.get('summary', {})
            
            total_sessions = summary.get('total_sessions', 0)
            total_messages = summary.get('total_messages', 0)
            total_actions = summary.get('total_actions', 0)
            
            # 使用量建议
            if total_sessions < 50:
                processed['recommendations'].append({
                    "category": "usage_improvement",
                    "priority": "high",
                    "title": "提升设备使用率",
                    "description": f"当前日使用量仅{total_sessions}次，建议加强推广和用户引导",
                    "actions": ["优化设备位置", "增加引导标识", "开展使用培训"]
                })
            
            # 交互质量建议
            if total_messages > 0 and total_sessions > 0:
                avg_messages = total_messages / total_sessions
                if avg_messages < 2:
                    processed['recommendations'].append({
                        "category": "interaction_quality",
                        "priority": "medium",
                        "title": "提升交互深度",
                        "description": f"平均每次对话仅{avg_messages:.1f}轮，建议优化对话引导",
                        "actions": ["改进欢迎语", "增加引导性问题", "优化回答质量"]
                    })
            
            # 功能使用建议
            if total_actions < total_sessions * 0.3:
                processed['opportunities'].append({
                    "category": "feature_adoption",
                    "title": "功能使用率提升机会",
                    "description": "用户较少使用高级功能，存在功能推广机会",
                    "potential": "可提升30%的功能使用率"
                })
        
        return processed
    
    def _process_default_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """默认数据处理"""
        return {
            "module_type": "default",
            "raw_data_summary": {
                "data_sources": list(raw_data.get('sources', {}).keys()),
                "time_range": raw_data.get('time_range', {}),
                "total_size": len(str(raw_data))
            },
            "processed_at": datetime.now().isoformat()
        }
