# -*- coding: utf-8 -*-
"""
报告相关数据模型 - 模块化版本
支持模块化报告生成、数据源选择、执行状态跟踪
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Enum, Index, JSON, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from ..database import Base_speech_ai_robot as Base


class ReportStatus(enum.Enum):
    """报告状态枚举"""
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReportType(enum.Enum):
    """报告类型枚举"""
    SINGLE_DAY = "single_day"
    MULTI_DAY = "multi_day"
    MODULAR = "modular"  # 新增：模块化报告


class PromptType(enum.Enum):
    """Prompt类型枚举"""
    SYSTEM = "system"
    USER = "user"
    CUSTOM = "custom"


class ModuleType(enum.Enum):
    """模块类型枚举"""
    BASIC_USAGE = "basic_usage"           # 基础使用数据、当天数据趋势、action数据统计
    EVENT_ANALYSIS = "event_analysis"     # 当日亮点事件和预警事件
    SERVICE_QUALITY = "service_quality"   # 当日满意服务案例和不满意的案例
    INSIGHT_ANALYSIS = "insight_analysis" # 一些新视角的观察
    BUSINESS_ADVICE = "business_advice"   # 提出一些经营建议


class DataSourceType(enum.Enum):
    """数据源类型枚举"""
    STATISTICS = "statistics"     # 统计数据
    CONVERSATION = "conversation" # 会话数据
    QUERY = "query"              # 查询数据
    HUMAN_STATS = "human_stats"  # 人脸识别数据
    MIXED = "mixed"              # 混合数据源


class ExecutionMode(enum.Enum):
    """执行模式枚举"""
    DIRECT_OUTPUT = "direct_output"    # 直接输出结果
    CHART_DISPLAY = "chart_display"    # 图表展示
    LLM_GENERATE = "llm_generate"      # 大模型生成
    TEMPLATE_RENDER = "template_render" # 模板渲染


class ModuleStatus(enum.Enum):
    """模块状态枚举"""
    PENDING = "pending"       # 等待执行
    RUNNING = "running"       # 执行中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 执行失败
    SKIPPED = "skipped"       # 跳过执行


class DeviceReport(Base):
    """设备报告生成记录表 - 记录请求参数和生成结果"""
    __tablename__ = "aos_device_reports"
    __table_args__ = {'comment': '设备报告生成记录表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    enterprise_id = Column(String(100), nullable=False, comment='企业ID')
    device_id = Column(String(100), nullable=False, comment='设备ID')
    report_type = Column(String(20), nullable=False, comment='报告类型')
    status = Column(String(20), default=ReportStatus.GENERATING.value, comment='报告状态')

    # 请求参数
    target_date = Column(String(10), nullable=False, comment='目标日期(YYYY-MM-DD)')
    start_date = Column(String(10), comment='开始日期(多天报告)')
    end_date = Column(String(10), comment='结束日期(多天报告)')
    prompt_version = Column(String(50), comment='Prompt版本')
    custom_system_prompt = Column(Text, comment='自定义系统Prompt')
    custom_user_prompt = Column(Text, comment='自定义用户Prompt')
    prompt_type = Column(String(20), default=PromptType.SYSTEM.value, comment='Prompt类型')

    # 模块化报告相关字段
    is_modular = Column(Boolean, default=False, comment='是否为模块化报告')
    module_config = Column(JSON, comment='模块配置信息')
    total_modules = Column(Integer, default=0, comment='总模块数')
    completed_modules = Column(Integer, default=0, comment='已完成模块数')

    # LLM生成结果
    system_prompt_used = Column(Text, comment='实际使用的系统Prompt')
    user_prompt_used = Column(Text, comment='实际使用的用户Prompt')
    generated_content = Column(Text, comment='生成的报告内容')
    model_name = Column(String(50), comment='使用的模型名称')

    # 性能指标
    content_length = Column(Integer, default=0, comment='内容长度')
    chunk_count = Column(Integer, default=0, comment='内容块数量')
    tokens_used = Column(Integer, comment='使用的Token数量')
    generation_start_time = Column(DateTime, comment='生成开始时间')
    generation_end_time = Column(DateTime, comment='生成结束时间')
    generation_duration = Column(Integer, comment='生成耗时(秒)')

    # 错误信息
    error_message = Column(Text, comment='错误信息')

    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 关联关系
    modules = relationship("ReportModule", back_populates="report", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_enterprise_device_date', 'enterprise_id', 'device_id', 'target_date'),
        Index('idx_report_type_status', 'report_type', 'status'),
        Index('idx_prompt_type', 'prompt_type'),
        Index('idx_prompt_version', 'prompt_version'),
        Index('idx_created_at', 'created_at'),
        Index('idx_is_modular', 'is_modular'),
        {'comment': '设备报告生成记录表'}
    )


class ReportModule(Base):
    """报告模块执行记录表"""
    __tablename__ = "aos_report_modules"
    __table_args__ = {'comment': '报告模块执行记录表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    report_id = Column(Integer, ForeignKey('aos_device_reports.id'), nullable=False, comment='报告ID')
    module_type = Column(String(50), nullable=False, comment='模块类型')
    module_name = Column(String(100), nullable=False, comment='模块名称')
    module_description = Column(Text, comment='模块描述')
    execution_order = Column(Integer, nullable=False, comment='执行顺序')
    status = Column(String(20), default=ModuleStatus.PENDING.value, comment='模块状态')

    # 数据源配置
    data_sources = Column(JSON, comment='数据源配置')
    primary_data_source = Column(String(50), comment='主要数据源类型')

    # 执行配置
    execution_mode = Column(String(50), default=ExecutionMode.LLM_GENERATE.value, comment='执行模式')
    use_llm = Column(Boolean, default=True, comment='是否使用大模型')
    template_name = Column(String(100), comment='模板名称')
    custom_config = Column(JSON, comment='自定义配置')

    # Prompt配置
    system_prompt = Column(Text, comment='系统Prompt')
    user_prompt = Column(Text, comment='用户Prompt')
    prompt_variables = Column(JSON, comment='Prompt变量')

    # 执行结果
    raw_data = Column(JSON, comment='原始数据')
    processed_data = Column(JSON, comment='处理后数据')
    generated_content = Column(Text, comment='生成的内容')
    chart_config = Column(JSON, comment='图表配置')
    interactive_data = Column(JSON, comment='交互数据(如session链接)')

    # 性能指标
    data_fetch_duration = Column(Integer, comment='数据获取耗时(毫秒)')
    processing_duration = Column(Integer, comment='数据处理耗时(毫秒)')
    generation_duration = Column(Integer, comment='内容生成耗时(毫秒)')
    total_duration = Column(Integer, comment='总耗时(毫秒)')
    tokens_used = Column(Integer, comment='使用的Token数量')

    # 错误信息
    error_message = Column(Text, comment='错误信息')
    error_details = Column(JSON, comment='错误详情')

    # 元数据
    started_at = Column(DateTime, comment='开始执行时间')
    completed_at = Column(DateTime, comment='完成时间')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 关联关系
    report = relationship("DeviceReport", back_populates="modules")

    # 索引
    __table_args__ = (
        Index('idx_report_id_order', 'report_id', 'execution_order'),
        Index('idx_module_type_status', 'module_type', 'status'),
        Index('idx_execution_mode', 'execution_mode'),
        Index('idx_primary_data_source', 'primary_data_source'),
        Index('idx_created_at', 'created_at'),
        {'comment': '报告模块执行记录表'}
    )


class ModuleTemplate(Base):
    """模块模板配置表"""
    __tablename__ = "aos_module_templates"
    __table_args__ = {'comment': '模块模板配置表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    template_name = Column(String(100), nullable=False, unique=True, comment='模板名称')
    module_type = Column(String(50), nullable=False, comment='模块类型')
    template_description = Column(Text, comment='模板描述')

    # 模板配置
    execution_mode = Column(String(50), default=ExecutionMode.LLM_GENERATE.value, comment='执行模式')
    data_source_config = Column(JSON, comment='数据源配置')
    prompt_template = Column(Text, comment='Prompt模板')
    output_template = Column(Text, comment='输出模板')
    chart_template = Column(JSON, comment='图表模板')

    # 变量定义
    required_variables = Column(JSON, comment='必需变量列表')
    optional_variables = Column(JSON, comment='可选变量列表')
    default_values = Column(JSON, comment='默认值配置')

    # 状态和版本
    is_active = Column(Boolean, default=True, comment='是否启用')
    version = Column(String(20), default='1.0', comment='模板版本')

    # 元数据
    created_by = Column(String(100), comment='创建者')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 索引
    __table_args__ = (
        Index('idx_template_name', 'template_name'),
        Index('idx_module_type_active', 'module_type', 'is_active'),
        Index('idx_execution_mode', 'execution_mode'),
        {'comment': '模块模板配置表'}
    )


class DataSourceConfig(Base):
    """数据源配置表"""
    __tablename__ = "aos_data_source_configs"
    __table_args__ = {'comment': '数据源配置表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    config_name = Column(String(100), nullable=False, unique=True, comment='配置名称')
    data_source_type = Column(String(50), nullable=False, comment='数据源类型')
    config_description = Column(Text, comment='配置描述')

    # 数据源配置
    connection_config = Column(JSON, comment='连接配置')
    query_config = Column(JSON, comment='查询配置')
    filter_config = Column(JSON, comment='过滤配置')
    aggregation_config = Column(JSON, comment='聚合配置')

    # 缓存配置
    enable_cache = Column(Boolean, default=True, comment='是否启用缓存')
    cache_duration = Column(Integer, default=3600, comment='缓存时长(秒)')

    # 状态
    is_active = Column(Boolean, default=True, comment='是否启用')

    # 元数据
    created_by = Column(String(100), comment='创建者')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 索引
    __table_args__ = (
        Index('idx_config_name', 'config_name'),
        Index('idx_data_source_type_active', 'data_source_type', 'is_active'),
        {'comment': '数据源配置表'}
    )