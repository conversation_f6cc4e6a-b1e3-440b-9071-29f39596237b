# -*- coding: utf-8 -*-
"""
设备报告生成API路由 - 简化版
只提供核心的SSE接口
"""

import logging
import re
from datetime import datetime, timedelta
import json

from fastapi import APIRouter, Query, Path, Body, Depends
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel, field_validator
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional

from app.services.device_report_service import device_report_service
from app.services.report_data_service import ReportDataService
from app.services.unified_data_service import UnifiedDataService
from app.config.report_prompts import ReportPrompts
from app.database import get_speech_ai_robot_db, get_agentos_db
from app.models.report_models import (
    ReportType, PromptType, ModuleType, DataSourceType,
    ExecutionMode, ModuleStatus
)


class MermaidOptimizeRequest(BaseModel):
    """Mermaid优化请求模型"""
    mermaid_code: str = Body(..., description="需要优化的Mermaid代码")
    prompt_version: str = Body("MERMAID_OPTIMIZE", description="Prompt版本")


class DeviceReportRequest(BaseModel):
    """设备报告生成请求模型"""
    date: str = None
    start_date: str = None
    end_date: str = None
    prompt_version: str = "UNIFIED_PROMPT_V2"
    custom_system_prompt: str = None
    custom_user_prompt: str = None
    prompt_type: str = "CUSTOM"
    model_name: str = None  # 新增：模型名称参数


class ModuleConfig(BaseModel):
    """模块配置模型"""
    module_type: str
    module_name: str
    module_description: str = ""
    data_sources: dict = {}
    primary_data_source: str
    execution_mode: str = ExecutionMode.LLM_GENERATE.value
    use_llm: bool = True
    template_name: str = None
    custom_config: dict = {}
    system_prompt: str = None
    user_prompt: str = None
    prompt_variables: dict = {}


class ModularReportRequest(BaseModel):
    """模块化报告生成请求模型"""
    target_date: str
    start_date: str = None
    end_date: str = None
    module_configs: List[ModuleConfig]
    model_name: str = None

    @field_validator('target_date', 'start_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        if v is not None:
            import re
            if not re.match(r'^\d{4}-\d{2}-\d{2}$', v):
                raise ValueError(f"日期格式错误: {v}，应为 YYYY-MM-DD 格式")
        return v


class DataSourceQueryRequest(BaseModel):
    """数据源查询请求模型"""
    data_source_type: str
    start_date: str
    end_date: str
    config: dict = {}

    @field_validator('start_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        if v is not None:
            import re
            if not re.match(r'^\d{4}-\d{2}-\d{2}$', v):
                raise ValueError(f"日期格式错误: {v}，应为 YYYY-MM-DD 格式")
        return v
    
    @field_validator('date', 'start_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        if v is not None:
            import re
            if not re.match(r'^\d{4}-\d{2}-\d{2}$', v):
                raise ValueError(f"日期格式错误: {v}，应为 YYYY-MM-DD 格式")
        return v
    
    @field_validator('prompt_version')
    @classmethod
    def validate_prompt_version(cls, v):
        valid_versions = ["UNIFIED_PROMPT", "UNIFIED_PROMPT_V2", "MULTI_DAY_PROMPT", "MERMAID_OPTIMIZE"]
        if v not in valid_versions:
            raise ValueError(f"Prompt版本错误: {v}，有效版本: {valid_versions}")
        return v


router = APIRouter(prefix="/device-reports", tags=["设备报告"])

logger = logging.getLogger(__name__)


@router.post(
    "/{enterprise_id}/{device_id}/generate",
    summary="生成设备使用报告 (SSE流式接口) - POST版本，支持自定义Prompt"
)
async def generate_device_report(
    enterprise_id: str = Path(..., description="企业ID"),
    device_id: str = Path(..., description="设备ID"),
    request: DeviceReportRequest = Body(..., description="报告生成请求参数"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """
    生成设备使用报告 - SSE流式接口 (POST版本，支持自定义Prompt)
    
    支持两种模式：
    1. 单天报告：使用 date 参数
    2. 多天报告：使用 start_date 和 end_date 参数
    
    支持自定义Prompt配置
    
    返回 Server-Sent Events 流，前端可以实时接收生成的报告内容
    """
    return await generate_device_report_internal(
        enterprise_id=enterprise_id,
        device_id=device_id,
        date=request.date,
        start_date=request.start_date,
        end_date=request.end_date,
        prompt_version=request.prompt_version,
        custom_prompt=request,
        model_name=request.model_name,
        db=db
    )


async def generate_device_report_internal(
    enterprise_id: str,
    device_id: str,
    date: str = None,
    start_date: str = None,
    end_date: str = None,
    prompt_version: str = "UNIFIED_PROMPT_V2",
    custom_prompt: DeviceReportRequest = None,
    model_name: str = None,
    db: Session = None
):
    """
    生成设备使用报告 - 内部实现函数
    """
    # 验证参数
    if date is None and (start_date is None or end_date is None):
        raise ValueError("必须提供 date 参数（单天模式）或 start_date 和 end_date 参数（多天模式）")
    
    # 确定报告类型和日期范围
    if date is not None:
        # 单天模式
        report_type = "single_day"
        target_date = date
        date_range = f"{date}"
    else:
        # 多天模式
        report_type = "multi_day"
        target_date = end_date  # 使用结束日期作为目标日期
        date_range = f"{start_date} 至 {end_date}"
    
    async def generate_report_stream():
        """生成报告流"""
        try:
            logger.info(f"开始生成设备报告: {enterprise_id}/{device_id}, 类型: {report_type}, 日期范围: {date_range}, Prompt版本: {prompt_version}")
            
            # 初始化报告数据服务
            report_data_service = ReportDataService(db)
            
            # 确定Prompt类型和自定义Prompt
            prompt_type = PromptType.SYSTEM
            custom_system_prompt = None
            custom_user_prompt = None
            
            if custom_prompt:
                logger.info(f"🔧 检测到自定义Prompt配置: {custom_prompt}")
                if custom_prompt.custom_system_prompt:
                    custom_system_prompt = custom_prompt.custom_system_prompt
                    prompt_type = PromptType.CUSTOM
                    logger.info(f"🔧 设置自定义系统Prompt: {custom_system_prompt[:100]}...")
                if custom_prompt.custom_user_prompt:
                    custom_user_prompt = custom_prompt.custom_user_prompt
                    prompt_type = PromptType.CUSTOM
                    logger.info(f"🔧 设置自定义用户Prompt: {custom_user_prompt[:100]}...")
            
            logger.info(f"🔧 最终Prompt配置: type={prompt_type}, system_prompt={'已设置' if custom_system_prompt else '未设置'}, user_prompt={'已设置' if custom_user_prompt else '未设置'}")
            logger.info(f"🔧 模型配置: model_name={model_name or '默认模型'}")
            
            # 创建报告记录
            report_record = report_data_service.create_report_record(
                enterprise_id=enterprise_id,
                device_id=device_id,
                report_type=ReportType.SINGLE_DAY if report_type == "single_day" else ReportType.MULTI_DAY,
                target_date=target_date,
                start_date=start_date,
                end_date=end_date,
                prompt_version=prompt_version,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                prompt_type=prompt_type,
                model_name=model_name
            )
            
            # 发送开始事件
            start_event = {
                "event": "start",
                "data": json.dumps({
                    "enterprise_id": enterprise_id,
                    "device_id": device_id,
                    "report_type": report_type,
                    "date_range": date_range,
                    "target_date": target_date,
                    "start_date": start_date,
                    "end_date": end_date,
                    "prompt_version": prompt_version,
                    "prompt_type": prompt_type.value,
                    "report_id": report_record.id,
                    "timestamp": datetime.now().isoformat(),
                    "message": f"开始生成{report_type}设备使用报告",
                    "batch": 1
                }, ensure_ascii=False)
            }
            logger.info(f"发送开始事件: {start_event}")
            yield start_event
            
            # 预检查数据是否存在
            data_exists = False
            if report_type == "single_day":
                # 单天模式：检查指定日期的数据
                chat_data = device_report_service._load_chat_data(enterprise_id, device_id, date)
                data_exists = (
                    chat_data and 
                    len(chat_data.strip()) > 0 and
                    "未找到企业" not in chat_data and 
                    "加载聊天数据时发生错误" not in chat_data
                )
            else:
                # 多天模式：检查日期范围内的数据
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                current_dt = start_dt
                
                while current_dt <= end_dt:
                    current_date = current_dt.strftime("%Y-%m-%d")
                    chat_data = device_report_service._load_chat_data(enterprise_id, device_id, current_date)
                    
                    if (
                        chat_data and 
                        len(chat_data.strip()) > 0 and
                        "未找到企业" not in chat_data and 
                        "加载聊天数据时发生错误" not in chat_data
                    ):
                        data_exists = True
                        break
                    
                    current_dt += timedelta(days=1)
            
            # 如果数据不存在，直接返回提示信息
            if not data_exists:
                logger.warning(f"未找到有效数据，跳过LLM调用: {enterprise_id}/{device_id}, 类型: {report_type}, 日期范围: {date_range}")
                
                # 发送数据检查结果
                data_check_event = {
                    "event": "batch_start",
                    "data": json.dumps({
                        "batch": 1,
                        "message": "数据检查中..."
                    }, ensure_ascii=False)
                }
                yield data_check_event
                
                # 发送错误内容
                error_content = f"# ❌ 数据检查结果\n\n"
                error_content += f"**企业ID**: {enterprise_id}\n"
                error_content += f"**设备ID**: {device_id}\n"
                error_content += f"**报告类型**: {report_type}\n"
                error_content += f"**日期范围**: {date_range}\n\n"
                error_content += f"⚠️ **未找到有效数据**\n\n"
                error_content += f"在指定的日期范围内未找到任何有效的聊天记录数据。\n\n"
                error_content += f"**可能的原因**:\n"
                error_content += f"- 该设备在指定日期没有产生聊天记录\n"
                error_content += f"- 数据文件不存在或路径错误\n"
                error_content += f"- 数据文件格式不正确\n\n"
                error_content += f"**建议操作**:\n"
                error_content += f"- 检查设备ID和企业ID是否正确\n"
                error_content += f"- 尝试选择其他日期范围\n"
                error_content += f"- 联系管理员[李东](https://applink.feishu.cn/client/chat/open?openId=ou_fba6f11c76e92582fe0b32fdbe9fc5c4)支持,确认数据状态\n\n"
                
                content_event = {
                    "event": "content",
                    "data": json.dumps({"content": error_content}, ensure_ascii=False)
                }
                yield content_event
                
                # 发送批次完成事件
                batch_complete_event = {
                    "event": "batch_complete",
                    "data": json.dumps({
                        "batch": 1,
                        "message": "数据检查完成"
                    }, ensure_ascii=False)
                }
                yield batch_complete_event
                
                # 发送完成事件
                complete_event = {
                    "event": "complete", 
                    "data": json.dumps({
                        "timestamp": datetime.now().isoformat(),
                        "message": f"{report_type}设备使用报告生成完成（无数据）"
                    }, ensure_ascii=False)
                }
                yield complete_event
                
                # 发送结束事件
                end_event = {
                    "event": "end",
                    "data": json.dumps({
                        "timestamp": datetime.now().isoformat(),
                        "message": "SSE连接即将关闭"
                    }, ensure_ascii=False)
                }
                yield end_event
                return
            
            # 数据存在，继续生成报告
            logger.info(f"数据检查通过，开始生成报告: {enterprise_id}/{device_id}, 类型: {report_type}")
            
            # 发送批次开始事件
            batch_start_event = {
                "event": "batch_start",
                "data": json.dumps({
                    "batch": 1,
                    "message": "开始生成报告内容"
                }, ensure_ascii=False)
            }
            yield batch_start_event
            
            # 设置报告数据服务
            device_report_service.report_data_service = report_data_service
            
            # 生成报告
            chunk_count = 0
            generated_content = ""
            try:
                if report_type == "single_day":
                    # 单天报告
                    async for result in device_report_service.generate_single_day_report(
                        enterprise_id=enterprise_id,
                        device_id=device_id,
                        date=date,
                        prompt_version=prompt_version,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        model_name=model_name,
                        report_id=report_record.id
                    ):
                        content_chunk, token_info = result
                        
                        if content_chunk is not None:
                            chunk_count += 1
                            generated_content += content_chunk
                            content_event = {
                                "event": "content",
                                "data": json.dumps({"content": content_chunk}, ensure_ascii=False)
                            }
                            yield content_event
                            
                            # 每100个块记录一次进度
                            if chunk_count % 100 == 0:
                                logger.info(f"已发送 {chunk_count} 个内容块")
                                # 更新内容块数量
                                report_data_service.update_chunk_count(report_record.id, chunk_count)
                        elif token_info is not None:
                            # 这是token信息，记录但不发送给前端
                            logger.info(f"📊 获取到单天报告Token使用量: {token_info.get('tokens_used', 0)}")
                else:
                    # 多天报告
                    async for result in device_report_service.generate_multi_day_report(
                        enterprise_id=enterprise_id,
                        device_id=device_id,
                        start_date=start_date,
                        end_date=end_date,
                        prompt_version=prompt_version,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        model_name=model_name,
                        report_id=report_record.id
                    ):
                        content_chunk, token_info = result
                        
                        if content_chunk is not None:
                            chunk_count += 1
                            generated_content += content_chunk
                            content_event = {
                                "event": "content",
                                "data": json.dumps({"content": content_chunk}, ensure_ascii=False)
                            }
                            yield content_event
                            
                            # 每100个块记录一次进度
                            if chunk_count % 100 == 0:
                                logger.info(f"已发送 {chunk_count} 个内容块")
                                # 更新内容块数量
                                report_data_service.update_chunk_count(report_record.id, chunk_count)
                        elif token_info is not None:
                            # 这是token信息，记录但不发送给前端
                            logger.info(f"📊 获取到多天报告Token使用量: {token_info.get('tokens_used', 0)}")
                
                logger.info(f"报告生成完成，共发送 {chunk_count} 个内容块")
                
                # 发送批次完成事件
                batch_complete_event = {
                    "event": "batch_complete",
                    "data": json.dumps({
                        "batch": 1,
                        "message": "报告内容生成完成"
                    }, ensure_ascii=False)
                }
                yield batch_complete_event
                
                # 报告生成完成，状态已在设备报告服务中更新
                pass
                
            except Exception as report_error:
                logger.error(f"报告生成过程中发生错误: {str(report_error)}")
                # 更新报告状态为失败
                report_data_service.update_report_failure(
                    report_id=report_record.id,
                    error_message=str(report_error)
                )
                raise report_error
            
            # 发送完成事件
            complete_event = {
                "event": "complete", 
                "data": json.dumps({
                    "timestamp": datetime.now().isoformat(),
                    "message": f"{report_type}设备使用报告生成完成"
                }, ensure_ascii=False)
            }
            logger.info(f"发送完成事件: {complete_event}")
            yield complete_event
            
            # 发送结束事件，明确告诉前端SSE连接即将关闭
            end_event = {
                "event": "end",
                "data": json.dumps({
                    "timestamp": datetime.now().isoformat(),
                    "message": "SSE连接即将关闭"
                }, ensure_ascii=False)
            }
            logger.info(f"发送结束事件: {end_event}")
            yield end_event
            
        except Exception as e:
            logger.error(f"生成报告流失败: {str(e)}")
            # 发送错误事件
            error_event = {
                "event": "error",
                "data": json.dumps({"error": str(e)}, ensure_ascii=False)
            }
            logger.error(f"发送错误事件: {error_event}")
            yield error_event
            
            # 发送结束事件，确保连接正确关闭
            end_event = {
                "event": "end",
                "data": json.dumps({
                    "timestamp": datetime.now().isoformat(),
                    "message": "SSE连接因错误而关闭"
                }, ensure_ascii=False)
            }
            logger.info(f"发送错误结束事件: {end_event}")
            yield end_event
    
    return EventSourceResponse(
        generate_report_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


# Mermaid格式优化接口
@router.post("/optimize-mermaid", summary="优化Mermaid图表格式")
async def optimize_mermaid_format(request: MermaidOptimizeRequest):
    """
    优化Mermaid图表格式 - 一次性返回接口
    
    功能：
    - 分析Mermaid代码格式问题
    - 自动修复语法错误
    - 优化代码结构
    - 确保图表能够正确渲染
    
    输入：
    - mermaid_code: 需要优化的Mermaid代码字符串
    
    输出：
    - optimized_code: 优化后的Mermaid代码
    - original_code: 原始代码
    - optimization_summary: 优化摘要
    
    数据传输方式：
    - 使用POST请求，通过JSON请求体传递Mermaid代码
    - 避免URL长度限制问题
    - 支持大段代码传输
    - 使用Pydantic模型进行数据验证
    """
    try:
        logger.info(f"开始优化Mermaid格式，代码长度: {len(request.mermaid_code)} 字符")
        
        # 系统提示词
        system_prompt = ReportPrompts.get_system_prompt()
        
        # 创建数据信息
        data_info = {
            'target_date': datetime.now().strftime("%Y-%m-%d"),
            'data_sources': [
                {
                    'date': datetime.now().strftime("%Y-%m-%d"),
                    'content': request.mermaid_code
                }
            ],
            'description': "以下是需要优化的Mermaid图表代码："
        }
        
        # 获取用户提示词
        user_prompt = ReportPrompts.get_unified_prompt(data_info, request.prompt_version)
        
        # 调用LLM服务进行优化
        llm_service = device_report_service._get_llm_service()
        
        # 收集完整的优化结果
        optimized_content = ""
        async for result in llm_service.generate_report_stage(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            stage_info={"name": "Mermaid格式优化", "prompt_version": request.prompt_version}
        ):
            content_chunk, token_info = result
            if content_chunk is not None:
                optimized_content += content_chunk
        
        # 清理优化结果，提取Mermaid代码块
        optimized_code = _extract_mermaid_code(optimized_content)
        
        # 生成优化摘要
        optimization_summary = _generate_optimization_summary(request.mermaid_code, optimized_code)
        
        logger.info(f"Mermaid格式优化完成，优化后代码长度: {len(optimized_code)} 字符")
        
        return {
            "status": "success",
            "original_code": request.mermaid_code,
            "optimized_code": optimized_code,
            "optimization_summary": optimization_summary,
            "timestamp": datetime.now().isoformat(),
            "message": "Mermaid格式优化完成"
        }
        
    except Exception as e:
        logger.error(f"Mermaid格式优化失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "original_code": request.mermaid_code,
            "optimized_code": "",
            "optimization_summary": "优化失败",
            "timestamp": datetime.now().isoformat(),
            "message": f"Mermaid格式优化失败: {str(e)}"
        }


def _extract_mermaid_code(content: str) -> str:
    """
    从LLM返回的内容中提取Mermaid代码块
    
    Args:
        content: LLM返回的完整内容
        
    Returns:
        提取的Mermaid代码
    """
    # 查找 ```mermaid 和 ``` 之间的内容
    import re
    
    # 匹配 ```mermaid ... ``` 模式
    mermaid_pattern = r'```mermaid\s*(.*?)\s*```'
    match = re.search(mermaid_pattern, content, re.DOTALL)
    
    if match:
        return match.group(1).strip()
    
    # 如果没有找到 ```mermaid 标记，尝试查找 ``` 包围的内容
    code_pattern = r'```\s*(.*?)\s*```'
    match = re.search(code_pattern, content, re.DOTALL)
    
    if match:
        return match.group(1).strip()
    
    # 如果都没有找到，返回原始内容（去除首尾空白）
    return content.strip()


def _generate_optimization_summary(original_code: str, optimized_code: str) -> dict:
    """
    生成优化摘要
    
    Args:
        original_code: 原始代码
        optimized_code: 优化后代码
        
    Returns:
        优化摘要字典
    """
    original_lines = len(original_code.split('\n'))
    optimized_lines = len(optimized_code.split('\n'))
    original_chars = len(original_code)
    optimized_chars = len(optimized_code)
    
    changes = {
        "lines_changed": abs(optimized_lines - original_lines),
        "chars_changed": abs(optimized_chars - original_chars),
        "original_lines": original_lines,
        "optimized_lines": optimized_lines,
        "original_chars": original_chars,
        "optimized_chars": optimized_chars
    }
    
    # 检测主要变化类型
    changes_detected = []
    
    if "```mermaid" not in original_code and "```mermaid" in optimized_code:
        changes_detected.append("添加了Mermaid代码块标记")
    
    if original_code != optimized_code:
        changes_detected.append("语法格式优化")
    
    if len(changes_detected) == 0:
        changes_detected.append("无需优化")
    
    return {
        "changes_detected": changes_detected,
        "statistics": changes,
        "optimization_needed": original_code != optimized_code
    }





@router.get("/reports", summary="获取报告生成记录列表")
async def get_report_records(
    enterprise_id: str = Query(..., description="企业ID"),
    device_id: str = Query(..., description="设备ID"),
    limit: int = Query(50, description="限制数量"),
    offset: int = Query(0, description="偏移量"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """获取报告生成记录列表"""
    try:
        report_data_service = ReportDataService(db)
        
        reports = report_data_service.get_reports_by_device(
            enterprise_id=enterprise_id,
            device_id=device_id,
            limit=limit,
            offset=offset
        )
        
        return {
            "status": "success",
            "data": [
                {
                    "id": report.id,
                    "enterprise_id": report.enterprise_id,
                    "device_id": report.device_id,
                    "report_type": report.report_type,
                    "status": report.status,
                    "target_date": report.target_date,
                    "start_date": report.start_date,
                    "end_date": report.end_date,
                    "prompt_version": report.prompt_version,
                    "prompt_type": report.prompt_type,
                    "model_name": report.model_name,
                    "content_length": report.content_length,
                    "chunk_count": report.chunk_count,
                    "generation_duration": report.generation_duration,
                    "created_at": report.created_at.isoformat(),
                    "updated_at": report.updated_at.isoformat()
                }
                for report in reports
            ]
        }
        
    except Exception as e:
        logger.error(f"获取报告生成记录列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取报告生成记录列表失败: {str(e)}"
        }


@router.get("/reports/{report_id}", summary="获取报告生成记录详情")
async def get_report_detail(
    report_id: int = Path(..., description="报告ID"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """获取报告生成记录详情"""
    try:
        report_data_service = ReportDataService(db)
        
        report = report_data_service.get_report_by_id(report_id)
        if not report:
            return {
                "status": "error",
                "message": "报告记录不存在"
            }
        
        return {
            "status": "success",
            "data": {
                "id": report.id,
                "enterprise_id": report.enterprise_id,
                "device_id": report.device_id,
                "report_type": report.report_type,
                "status": report.status,
                "target_date": report.target_date,
                "start_date": report.start_date,
                "end_date": report.end_date,
                "prompt_version": report.prompt_version,
                "custom_system_prompt": report.custom_system_prompt,
                "custom_user_prompt": report.custom_user_prompt,
                "prompt_type": report.prompt_type,
                "system_prompt_used": report.system_prompt_used,
                "user_prompt_used": report.user_prompt_used,
                "generated_content": report.generated_content,
                "model_name": report.model_name,
                "content_length": report.content_length,
                "chunk_count": report.chunk_count,
                "tokens_used": report.tokens_used,
                "generation_start_time": report.generation_start_time.isoformat() if report.generation_start_time else None,
                "generation_end_time": report.generation_end_time.isoformat() if report.generation_end_time else None,
                "generation_duration": report.generation_duration,
                "error_message": report.error_message,
                "created_at": report.created_at.isoformat(),
                "updated_at": report.updated_at.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取报告生成记录详情失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取报告生成记录详情失败: {str(e)}"
        }


@router.get("/models", summary="获取可用模型列表")
async def get_available_models():
    """获取所有可用的LLM模型列表"""
    try:
        from app.config.llm_models import LLMModelConfig, RECOMMENDED_MODELS
        
        # 获取所有模型信息
        all_models = LLMModelConfig.get_all_model_info()
        
        # 按提供商分组
        models_by_provider = {}
        for model_name, model_info in all_models.items():
            if model_info:
                provider = model_info["provider"]
                if provider not in models_by_provider:
                    models_by_provider[provider] = []
                models_by_provider[provider].append(model_info)
        
        return {
            "status": "success",
            "data": {
                "all_models": all_models,
                "models_by_provider": models_by_provider,
                "recommended_models": RECOMMENDED_MODELS,
                "total_count": len([m for m in all_models.values() if m])
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取可用模型列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取可用模型列表失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/models/{model_name}", summary="获取模型详细信息")
async def get_model_info(model_name: str = Path(..., description="模型名称")):
    """获取指定模型的详细信息"""
    try:
        from app.config.llm_models import LLMModelConfig
        
        model_info = LLMModelConfig.get_model_info(model_name)
        if not model_info:
            return {
                "status": "error",
                "message": f"模型不存在: {model_name}",
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "status": "success",
            "data": model_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取模型信息失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


# ==================== 模块化报告相关API ====================

@router.post("/{enterprise_id}/{device_id}/modular", summary="生成模块化报告")
async def generate_modular_report(
    enterprise_id: str = Path(..., description="企业ID"),
    device_id: str = Path(..., description="设备ID"),
    request: ModularReportRequest = Body(..., description="模块化报告请求"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """
    生成模块化报告 - SSE流式响应

    支持5个独立模块的分步执行：
    1. 基础使用数据、当天数据趋势、action数据统计
    2. 当日亮点事件和预警事件
    3. 当日满意服务案例和不满意的案例
    4. 一些新视角的观察
    5. 提出一些经营建议
    """

    async def generate():
        try:
            logger.info(f"开始生成模块化报告: {enterprise_id}/{device_id}")

            # 初始化报告数据服务
            report_data_service = ReportDataService(db)
            device_report_service.report_data_service = report_data_service

            # 转换模块配置
            module_configs = []
            for module_config in request.module_configs:
                config_dict = {
                    "module_type": module_config.module_type,
                    "module_name": module_config.module_name,
                    "module_description": module_config.module_description,
                    "data_sources": module_config.data_sources,
                    "primary_data_source": module_config.primary_data_source,
                    "execution_mode": module_config.execution_mode,
                    "use_llm": module_config.use_llm,
                    "template_name": module_config.template_name,
                    "custom_config": module_config.custom_config,
                    "system_prompt": module_config.system_prompt,
                    "user_prompt": module_config.user_prompt,
                    "prompt_variables": module_config.prompt_variables
                }
                module_configs.append(config_dict)

            # 生成模块化报告
            async for content, metadata in device_report_service.generate_modular_report(
                enterprise_id=enterprise_id,
                device_id=device_id,
                target_date=request.target_date,
                module_configs=module_configs,
                start_date=request.start_date,
                end_date=request.end_date,
                model_name=request.model_name
            ):
                if content:
                    data = {
                        "content": content,
                        "metadata": metadata or {},
                        "timestamp": datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

            # 发送完成信号
            yield f"data: {json.dumps({'type': 'complete', 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"

        except Exception as e:
            logger.error(f"生成模块化报告失败: {str(e)}")
            error_data = {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    return EventSourceResponse(generate())


@router.get("/{enterprise_id}/{device_id}/modules", summary="获取报告模块列表")
async def get_report_modules(
    enterprise_id: str = Path(..., description="企业ID"),
    device_id: str = Path(..., description="设备ID"),
    report_id: int = Query(..., description="报告ID"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """获取指定报告的模块列表"""
    try:
        report_data_service = ReportDataService(db)
        modules = report_data_service.get_report_modules(report_id)

        module_list = []
        for module in modules:
            module_data = {
                "id": module.id,
                "module_type": module.module_type,
                "module_name": module.module_name,
                "module_description": module.module_description,
                "execution_order": module.execution_order,
                "status": module.status,
                "primary_data_source": module.primary_data_source,
                "execution_mode": module.execution_mode,
                "use_llm": module.use_llm,
                "started_at": module.started_at.isoformat() if module.started_at else None,
                "completed_at": module.completed_at.isoformat() if module.completed_at else None,
                "error_message": module.error_message,
                "tokens_used": module.tokens_used,
                "total_duration": module.total_duration
            }
            module_list.append(module_data)

        return {
            "status": "success",
            "data": {
                "report_id": report_id,
                "modules": module_list,
                "total_modules": len(module_list)
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取报告模块列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取报告模块列表失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/templates", summary="获取模块模板列表")
async def get_module_templates(
    module_type: str = Query(None, description="模块类型过滤"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """获取可用的模块模板列表"""
    try:
        report_data_service = ReportDataService(db)
        templates = report_data_service.get_module_templates(module_type=module_type)

        template_list = []
        for template in templates:
            template_data = {
                "id": template.id,
                "template_name": template.template_name,
                "module_type": template.module_type,
                "description": template.description,
                "default_config": template.default_config,
                "system_prompt": template.system_prompt,
                "user_prompt": template.user_prompt,
                "prompt_variables": template.prompt_variables,
                "is_active": template.is_active,
                "created_at": template.created_at.isoformat() if template.created_at else None
            }
            template_list.append(template_data)

        return {
            "status": "success",
            "data": {
                "templates": template_list,
                "total_count": len(template_list),
                "module_types": list(set([t.module_type for t in templates]))
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取模块模板列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取模块模板列表失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/data-sources", summary="获取数据源配置列表")
async def get_data_source_configs(
    data_source_type: str = Query(None, description="数据源类型过滤"),
    db: Session = Depends(get_speech_ai_robot_db)
):
    """获取可用的数据源配置列表"""
    try:
        report_data_service = ReportDataService(db)
        configs = report_data_service.get_data_source_configs(data_source_type=data_source_type)

        config_list = []
        for config in configs:
            config_data = {
                "id": config.id,
                "config_name": config.config_name,
                "data_source_type": config.data_source_type,
                "description": config.description,
                "connection_config": config.connection_config,
                "query_config": config.query_config,
                "is_active": config.is_active,
                "created_at": config.created_at.isoformat() if config.created_at else None
            }
            config_list.append(config_data)

        return {
            "status": "success",
            "data": {
                "configs": config_list,
                "total_count": len(config_list),
                "data_source_types": list(set([c.data_source_type for c in configs]))
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取数据源配置列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取数据源配置列表失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.post("/{enterprise_id}/{device_id}/data-query", summary="查询数据源数据")
async def query_data_source(
    enterprise_id: str = Path(..., description="企业ID"),
    device_id: str = Path(..., description="设备ID"),
    request: DataSourceQueryRequest = Body(..., description="数据源查询请求"),
    agentos_db: Session = Depends(get_agentos_db),
    speech_ai_robot_db: Session = Depends(get_speech_ai_robot_db)
):
    """查询指定数据源的数据"""
    try:
        from datetime import datetime as dt

        # 初始化统一数据服务
        unified_service = UnifiedDataService(agentos_db, speech_ai_robot_db)

        # 转换日期格式
        start_time = dt.strptime(request.start_date, '%Y-%m-%d')
        end_time = dt.strptime(request.end_date, '%Y-%m-%d')

        # 查询数据
        data = unified_service.fetch_data(
            data_source_type=request.data_source_type,
            enterprise_id=enterprise_id,
            device_id=device_id,
            start_time=start_time,
            end_time=end_time,
            config=request.config
        )

        return {
            "status": "success",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"查询数据源数据失败: {str(e)}")
        return {
            "status": "error",
            "message": f"查询数据源数据失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/data-sources/schemas", summary="获取数据源结构信息")
async def get_data_source_schemas(
    agentos_db: Session = Depends(get_agentos_db),
    speech_ai_robot_db: Session = Depends(get_speech_ai_robot_db)
):
    """获取所有数据源的结构信息"""
    try:
        # 初始化统一数据服务
        unified_service = UnifiedDataService(agentos_db, speech_ai_robot_db)

        # 获取数据源结构
        schemas = unified_service.get_available_data_sources()

        return {
            "status": "success",
            "data": {
                "schemas": schemas,
                "total_count": len(schemas)
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取数据源结构信息失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取数据源结构信息失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/module-types", summary="获取模块类型列表")
async def get_module_types():
    """获取支持的模块类型列表"""
    try:
        module_types = [
            {
                "type": ModuleType.BASIC_USAGE.value,
                "name": "基础使用数据",
                "description": "基础使用数据、当天数据趋势、action数据统计",
                "default_data_sources": [DataSourceType.STATISTICS.value, DataSourceType.CONVERSATION.value]
            },
            {
                "type": ModuleType.EVENT_ANALYSIS.value,
                "name": "事件分析",
                "description": "当日亮点事件和预警事件",
                "default_data_sources": [DataSourceType.STATISTICS.value, DataSourceType.CONVERSATION.value]
            },
            {
                "type": ModuleType.SERVICE_QUALITY.value,
                "name": "服务质量",
                "description": "当日满意服务案例和不满意的案例",
                "default_data_sources": [DataSourceType.CONVERSATION.value, DataSourceType.STATISTICS.value]
            },
            {
                "type": ModuleType.INSIGHT_ANALYSIS.value,
                "name": "洞察分析",
                "description": "一些新视角的观察",
                "default_data_sources": [DataSourceType.MIXED.value]
            },
            {
                "type": ModuleType.BUSINESS_ADVICE.value,
                "name": "业务建议",
                "description": "提出一些经营建议",
                "default_data_sources": [DataSourceType.MIXED.value]
            }
        ]

        execution_modes = [
            {
                "mode": ExecutionMode.DIRECT_OUTPUT.value,
                "name": "直接输出",
                "description": "直接输出处理后的数据"
            },
            {
                "mode": ExecutionMode.TEMPLATE_RENDER.value,
                "name": "模板渲染",
                "description": "使用模板渲染数据"
            },
            {
                "mode": ExecutionMode.CHART_DISPLAY.value,
                "name": "图表展示",
                "description": "生成图表配置和展示"
            },
            {
                "mode": ExecutionMode.LLM_GENERATE.value,
                "name": "AI生成",
                "description": "使用大语言模型生成内容"
            }
        ]

        data_source_types = [
            {
                "type": DataSourceType.STATISTICS.value,
                "name": "统计数据",
                "description": "从统计表获取聚合数据"
            },
            {
                "type": DataSourceType.CONVERSATION.value,
                "name": "会话数据",
                "description": "从会话表获取原始对话数据"
            },
            {
                "type": DataSourceType.QUERY.value,
                "name": "查询数据",
                "description": "从查询日志获取用户查询数据"
            },
            {
                "type": DataSourceType.HUMAN_STATS.value,
                "name": "人脸识别数据",
                "description": "从人脸识别系统获取访客数据"
            },
            {
                "type": DataSourceType.MIXED.value,
                "name": "混合数据源",
                "description": "组合多个数据源的数据"
            }
        ]

        return {
            "status": "success",
            "data": {
                "module_types": module_types,
                "execution_modes": execution_modes,
                "data_source_types": data_source_types
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取模块类型列表失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取模块类型列表失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


# 保留一个简单的健康检查
@router.get("/health", summary="健康检查")
async def health_check():
    """报告服务健康检查"""
    return {
        "status": "healthy",
        "service": "device-reports",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }