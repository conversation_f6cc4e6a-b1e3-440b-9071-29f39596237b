import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.services.device_report_service import DeviceReportService
from app.services.unified_data_service import UnifiedDataService
from app.services.modular_report_helpers import ModularReportHelpers
from app.models.report_models import ModuleType, DataSourceType, ExecutionMode, ModuleStatus


class TestModularReportService:
    """模块化报告服务测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_unified_data_service(self):
        """模拟统一数据服务"""
        service = Mock(spec=UnifiedDataService)
        service.fetch_data = AsyncMock(return_value={
            'summary': {'total_sessions': 100, 'avg_duration': 300},
            'trends': {'hourly': [{'hour': '09:00', 'sessions': 10}]},
            'events': {'login': {'count': 50}},
            'quality_metrics': {'satisfaction': 4.2}
        })
        return service

    @pytest.fixture
    def mock_report_helpers(self):
        """模拟报告助手"""
        helpers = Mock(spec=ModularReportHelpers)
        helpers.fetch_module_data = AsyncMock(return_value={'test': 'data'})
        helpers.process_module_data = Mock(return_value={'processed': 'data'})
        return helpers

    @pytest.fixture
    def device_report_service(self, mock_db_session):
        """创建设备报告服务实例"""
        service = DeviceReportService()
        service.report_data_service = Mock()
        service.unified_data_service = Mock()
        service.modular_helpers = Mock()
        return service

    @pytest.mark.asyncio
    async def test_generate_modular_report_success(self, device_report_service):
        """测试模块化报告生成成功"""
        # 准备测试数据
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        target_date = "2024-01-01"
        module_configs = [
            {
                "module_type": ModuleType.BASIC_USAGE.value,
                "module_name": "基础使用数据",
                "module_description": "测试模块",
                "data_sources": {"statistics": {}},
                "primary_data_source": DataSourceType.STATISTICS.value,
                "execution_mode": ExecutionMode.LLM_GENERATE.value,
                "use_llm": True,
                "custom_config": {},
                "prompt_variables": {}
            }
        ]

        # 模拟依赖服务
        device_report_service.report_data_service.create_modular_report = Mock(return_value=Mock(id=1))
        device_report_service.report_data_service.update_module_status = Mock()
        device_report_service.report_data_service.update_module_result = Mock()
        
        # 模拟模块执行
        with patch.object(device_report_service, '_execute_single_module') as mock_execute:
            mock_execute.return_value = ("测试内容", {"tokens_used": 100})
            
            # 执行测试
            results = []
            async for content, metadata in device_report_service.generate_modular_report(
                enterprise_id=enterprise_id,
                device_id=device_id,
                target_date=target_date,
                module_configs=module_configs
            ):
                results.append((content, metadata))

            # 验证结果
            assert len(results) > 0
            assert any("测试内容" in result[0] for result in results if result[0])

    @pytest.mark.asyncio
    async def test_execute_single_module_llm_generate(self, device_report_service):
        """测试单个模块执行 - LLM生成模式"""
        module_config = {
            "module_type": ModuleType.BASIC_USAGE.value,
            "module_name": "基础使用数据",
            "execution_mode": ExecutionMode.LLM_GENERATE.value,
            "use_llm": True,
            "data_sources": {"statistics": {}},
            "primary_data_source": DataSourceType.STATISTICS.value
        }

        # 模拟数据获取和处理
        with patch.object(device_report_service, 'modular_helpers') as mock_helpers:
            mock_helpers.fetch_module_data = AsyncMock(return_value={"test": "data"})
            mock_helpers.process_module_data = Mock(return_value={"processed": "data"})
            
            with patch.object(device_report_service, '_generate_llm_content') as mock_llm:
                mock_llm.return_value = ("AI生成的内容", 150)
                
                # 执行测试
                content, metadata = await device_report_service._execute_single_module(
                    module_config=module_config,
                    enterprise_id="test_enterprise",
                    device_id="test_device",
                    target_date="2024-01-01"
                )

                # 验证结果
                assert content == "AI生成的内容"
                assert metadata["tokens_used"] == 150

    @pytest.mark.asyncio
    async def test_execute_single_module_direct_output(self, device_report_service):
        """测试单个模块执行 - 直接输出模式"""
        module_config = {
            "module_type": ModuleType.BASIC_USAGE.value,
            "module_name": "基础使用数据",
            "execution_mode": ExecutionMode.DIRECT_OUTPUT.value,
            "use_llm": False,
            "data_sources": {"statistics": {}},
            "primary_data_source": DataSourceType.STATISTICS.value
        }

        # 模拟数据获取和处理
        with patch.object(device_report_service, 'modular_helpers') as mock_helpers:
            mock_helpers.fetch_module_data = AsyncMock(return_value={"test": "data"})
            mock_helpers.process_module_data = Mock(return_value={
                "summary": {"total_sessions": 100},
                "trends": {"hourly": [{"hour": "09:00", "sessions": 10}]}
            })
            
            with patch.object(device_report_service, '_generate_direct_output') as mock_direct:
                mock_direct.return_value = "直接输出内容"
                
                # 执行测试
                content, metadata = await device_report_service._execute_single_module(
                    module_config=module_config,
                    enterprise_id="test_enterprise",
                    device_id="test_device",
                    target_date="2024-01-01"
                )

                # 验证结果
                assert content == "直接输出内容"
                assert "execution_time" in metadata

    @pytest.mark.asyncio
    async def test_execute_single_module_chart_display(self, device_report_service):
        """测试单个模块执行 - 图表展示模式"""
        module_config = {
            "module_type": ModuleType.BASIC_USAGE.value,
            "module_name": "基础使用数据",
            "execution_mode": ExecutionMode.CHART_DISPLAY.value,
            "use_llm": False,
            "data_sources": {"statistics": {}},
            "primary_data_source": DataSourceType.STATISTICS.value
        }

        # 模拟数据获取和处理
        with patch.object(device_report_service, 'modular_helpers') as mock_helpers:
            mock_helpers.fetch_module_data = AsyncMock(return_value={"test": "data"})
            mock_helpers.process_module_data = Mock(return_value={
                "trends": {"hourly": [{"hour": "09:00", "sessions": 10}]}
            })
            
            with patch.object(device_report_service, '_generate_chart_config') as mock_chart:
                mock_chart.return_value = ("图表内容", {"type": "line", "data": []})
                
                # 执行测试
                content, metadata = await device_report_service._execute_single_module(
                    module_config=module_config,
                    enterprise_id="test_enterprise",
                    device_id="test_device",
                    target_date="2024-01-01"
                )

                # 验证结果
                assert content == "图表内容"
                assert "chart_config" in metadata

    def test_generate_direct_output(self, device_report_service):
        """测试直接输出生成"""
        processed_data = {
            "summary": {
                "total_sessions": 100,
                "avg_duration": 300
            },
            "trends": {
                "hourly": [{"hour": "09:00", "sessions": 10}]
            }
        }
        module_config = {
            "module_name": "测试模块"
        }

        content = device_report_service._generate_direct_output(processed_data, module_config)
        
        assert "测试模块" in content
        assert "数据摘要" in content
        assert "total_sessions: 100" in content

    def test_generate_chart_config_basic_usage(self, device_report_service):
        """测试基础使用数据图表配置生成"""
        processed_data = {
            "trends": {
                "hourly": [
                    {"hour": "09:00", "sessions": 10},
                    {"hour": "10:00", "sessions": 15}
                ]
            },
            "summary": {"total_sessions": 25}
        }
        module_config = {
            "module_name": "基础使用数据",
            "module_type": ModuleType.BASIC_USAGE.value
        }

        content, chart_config = device_report_service._generate_chart_config(processed_data, module_config)
        
        assert "基础使用数据" in content
        assert chart_config["type"] == "line"
        assert "data" in chart_config
        assert len(chart_config["data"]["labels"]) == 2

    def test_generate_chart_config_event_analysis(self, device_report_service):
        """测试事件分析图表配置生成"""
        processed_data = {
            "events": {
                "login": {"count": 50},
                "logout": {"count": 45}
            },
            "summary": {"total_events": 95}
        }
        module_config = {
            "module_name": "事件分析",
            "module_type": ModuleType.EVENT_ANALYSIS.value
        }

        content, chart_config = device_report_service._generate_chart_config(processed_data, module_config)
        
        assert "事件分析" in content
        assert chart_config["type"] == "bar"
        assert len(chart_config["data"]["labels"]) == 2

    @pytest.mark.asyncio
    async def test_generate_llm_content(self, device_report_service):
        """测试LLM内容生成"""
        processed_data = {"test": "data"}
        module_config = {
            "module_name": "测试模块",
            "module_type": ModuleType.BASIC_USAGE.value,
            "system_prompt": "你是一个测试助手",
            "user_prompt": "请分析数据: {data}",
            "prompt_variables": {"custom_var": "test_value"}
        }

        # 模拟LLM服务
        mock_llm_service = Mock()
        mock_llm_service.generate_response = AsyncMock(return_value={
            "content": "AI生成的测试内容",
            "tokens_used": 200
        })
        
        with patch.object(device_report_service, '_get_llm_service', return_value=mock_llm_service):
            content, tokens_used = await device_report_service._generate_llm_content(
                processed_data, module_config
            )

            assert content == "AI生成的测试内容"
            assert tokens_used == 200

    def test_get_default_system_prompt(self, device_report_service):
        """测试默认系统提示词获取"""
        for module_type in ModuleType:
            prompt = device_report_service._get_default_system_prompt(module_type.value)
            assert isinstance(prompt, str)
            assert len(prompt) > 0

    def test_get_default_user_prompt(self, device_report_service):
        """测试默认用户提示词获取"""
        for module_type in ModuleType:
            prompt = device_report_service._get_default_user_prompt(module_type.value)
            assert isinstance(prompt, str)
            assert "{data}" in prompt

    @pytest.mark.asyncio
    async def test_module_execution_error_handling(self, device_report_service):
        """测试模块执行错误处理"""
        module_config = {
            "module_type": ModuleType.BASIC_USAGE.value,
            "module_name": "错误测试模块",
            "execution_mode": ExecutionMode.LLM_GENERATE.value,
            "use_llm": True,
            "data_sources": {"statistics": {}},
            "primary_data_source": DataSourceType.STATISTICS.value
        }

        # 模拟数据获取失败
        with patch.object(device_report_service, 'modular_helpers') as mock_helpers:
            mock_helpers.fetch_module_data = AsyncMock(side_effect=Exception("数据获取失败"))
            
            # 执行测试
            content, metadata = await device_report_service._execute_single_module(
                module_config=module_config,
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01"
            )

            # 验证错误处理
            assert "执行失败" in content
            assert "error" in metadata

    @pytest.mark.asyncio
    async def test_generate_modular_report_empty_configs(self, device_report_service):
        """测试空模块配置的处理"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        target_date = "2024-01-01"
        module_configs = []

        # 模拟依赖服务
        device_report_service.report_data_service.create_modular_report = Mock(return_value=Mock(id=1))
        
        # 执行测试
        results = []
        async for content, metadata in device_report_service.generate_modular_report(
            enterprise_id=enterprise_id,
            device_id=device_id,
            target_date=target_date,
            module_configs=module_configs
        ):
            results.append((content, metadata))

        # 验证结果
        assert len(results) > 0
        # 应该有报告创建和完成的消息
        assert any(metadata and metadata.get("type") == "report_created" for _, metadata in results)
        assert any(metadata and metadata.get("type") == "complete" for _, metadata in results)
