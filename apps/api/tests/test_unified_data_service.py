import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from app.services.unified_data_service import (
    UnifiedDataService, StatisticsDataSource, ConversationDataSource,
    QueryDataSource, HumanStatsDataSource, BaseDataSource
)
from app.models.report_models import DataSourceType


class TestUnifiedDataService:
    """统一数据服务测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()

    @pytest.fixture
    def unified_data_service(self, mock_db_session):
        """创建统一数据服务实例"""
        return UnifiedDataService(db_session=mock_db_session)

    @pytest.mark.asyncio
    async def test_fetch_data_statistics_source(self, unified_data_service):
        """测试统计数据源获取"""
        config = {
            "data_source_type": DataSourceType.STATISTICS.value,
            "query_config": {
                "metrics": ["session_count", "avg_duration"],
                "time_range": "1d"
            }
        }

        # 模拟统计数据源
        with patch.object(unified_data_service, '_get_data_source') as mock_get_source:
            mock_source = Mock(spec=StatisticsDataSource)
            mock_source.fetch_data = AsyncMock(return_value={
                "session_count": 100,
                "avg_duration": 300
            })
            mock_get_source.return_value = mock_source

            result = await unified_data_service.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["session_count"] == 100
            assert result["avg_duration"] == 300

    @pytest.mark.asyncio
    async def test_fetch_data_conversation_source(self, unified_data_service):
        """测试对话数据源获取"""
        config = {
            "data_source_type": DataSourceType.CONVERSATION.value,
            "query_config": {
                "include_messages": True,
                "satisfaction_filter": ">=4"
            }
        }

        # 模拟对话数据源
        with patch.object(unified_data_service, '_get_data_source') as mock_get_source:
            mock_source = Mock(spec=ConversationDataSource)
            mock_source.fetch_data = AsyncMock(return_value={
                "conversations": [
                    {"id": "conv1", "satisfaction": 4.5},
                    {"id": "conv2", "satisfaction": 4.2}
                ]
            })
            mock_get_source.return_value = mock_source

            result = await unified_data_service.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert len(result["conversations"]) == 2
            assert result["conversations"][0]["satisfaction"] == 4.5

    @pytest.mark.asyncio
    async def test_fetch_data_mixed_sources(self, unified_data_service):
        """测试混合数据源获取"""
        config = {
            "data_source_type": DataSourceType.MIXED.value,
            "sources": [
                {
                    "type": DataSourceType.STATISTICS.value,
                    "weight": 0.6,
                    "query_config": {"metrics": ["session_count"]}
                },
                {
                    "type": DataSourceType.CONVERSATION.value,
                    "weight": 0.4,
                    "query_config": {"include_messages": False}
                }
            ]
        }

        # 模拟多个数据源
        with patch.object(unified_data_service, '_get_data_source') as mock_get_source:
            def side_effect(source_type, *args, **kwargs):
                if source_type == DataSourceType.STATISTICS:
                    mock_source = Mock(spec=StatisticsDataSource)
                    mock_source.fetch_data = AsyncMock(return_value={"session_count": 100})
                    return mock_source
                elif source_type == DataSourceType.CONVERSATION:
                    mock_source = Mock(spec=ConversationDataSource)
                    mock_source.fetch_data = AsyncMock(return_value={"conversations": []})
                    return mock_source
                
            mock_get_source.side_effect = side_effect

            result = await unified_data_service.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert "statistics" in result
            assert "conversation" in result
            assert result["statistics"]["session_count"] == 100

    def test_get_data_source_statistics(self, unified_data_service):
        """测试获取统计数据源"""
        source = unified_data_service._get_data_source(
            DataSourceType.STATISTICS,
            unified_data_service.db_session
        )
        assert isinstance(source, StatisticsDataSource)

    def test_get_data_source_conversation(self, unified_data_service):
        """测试获取对话数据源"""
        source = unified_data_service._get_data_source(
            DataSourceType.CONVERSATION,
            unified_data_service.db_session
        )
        assert isinstance(source, ConversationDataSource)

    def test_get_data_source_query(self, unified_data_service):
        """测试获取查询数据源"""
        source = unified_data_service._get_data_source(
            DataSourceType.QUERY,
            unified_data_service.db_session
        )
        assert isinstance(source, QueryDataSource)

    def test_get_data_source_human_stats(self, unified_data_service):
        """测试获取人工统计数据源"""
        source = unified_data_service._get_data_source(
            DataSourceType.HUMAN_STATS,
            unified_data_service.db_session
        )
        assert isinstance(source, HumanStatsDataSource)

    def test_get_data_source_invalid_type(self, unified_data_service):
        """测试无效数据源类型"""
        with pytest.raises(ValueError, match="不支持的数据源类型"):
            unified_data_service._get_data_source(
                "invalid_type",
                unified_data_service.db_session
            )

    @pytest.mark.asyncio
    async def test_validate_config_valid(self, unified_data_service):
        """测试有效配置验证"""
        config = {
            "data_source_type": DataSourceType.STATISTICS.value,
            "query_config": {"metrics": ["session_count"]}
        }

        # 应该不抛出异常
        await unified_data_service._validate_config(config)

    @pytest.mark.asyncio
    async def test_validate_config_missing_type(self, unified_data_service):
        """测试缺少数据源类型的配置"""
        config = {
            "query_config": {"metrics": ["session_count"]}
        }

        with pytest.raises(ValueError, match="缺少数据源类型"):
            await unified_data_service._validate_config(config)

    @pytest.mark.asyncio
    async def test_validate_config_invalid_type(self, unified_data_service):
        """测试无效数据源类型的配置"""
        config = {
            "data_source_type": "invalid_type",
            "query_config": {"metrics": ["session_count"]}
        }

        with pytest.raises(ValueError, match="无效的数据源类型"):
            await unified_data_service._validate_config(config)


class TestStatisticsDataSource:
    """统计数据源测试类"""

    @pytest.fixture
    def statistics_data_source(self):
        """创建统计数据源实例"""
        return StatisticsDataSource(Mock())

    @pytest.mark.asyncio
    async def test_fetch_data_basic_metrics(self, statistics_data_source):
        """测试基础指标获取"""
        config = {
            "metrics": ["session_count", "avg_duration"],
            "time_range": "1d"
        }

        # 模拟数据库查询
        with patch.object(statistics_data_source, '_query_statistics') as mock_query:
            mock_query.return_value = {
                "session_count": 150,
                "avg_duration": 280,
                "total_duration": 42000
            }

            result = await statistics_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["session_count"] == 150
            assert result["avg_duration"] == 280

    @pytest.mark.asyncio
    async def test_fetch_data_with_trends(self, statistics_data_source):
        """测试带趋势数据获取"""
        config = {
            "metrics": ["session_count"],
            "time_range": "1d",
            "include_trends": True,
            "trend_granularity": "hourly"
        }

        # 模拟数据库查询
        with patch.object(statistics_data_source, '_query_statistics') as mock_query:
            mock_query.return_value = {"session_count": 100}
            
        with patch.object(statistics_data_source, '_query_trends') as mock_trends:
            mock_trends.return_value = {
                "hourly": [
                    {"hour": "09:00", "sessions": 10},
                    {"hour": "10:00", "sessions": 15}
                ]
            }

            result = await statistics_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["session_count"] == 100
            assert "trends" in result
            assert len(result["trends"]["hourly"]) == 2


class TestConversationDataSource:
    """对话数据源测试类"""

    @pytest.fixture
    def conversation_data_source(self):
        """创建对话数据源实例"""
        return ConversationDataSource(Mock())

    @pytest.mark.asyncio
    async def test_fetch_data_basic(self, conversation_data_source):
        """测试基础对话数据获取"""
        config = {
            "include_messages": False,
            "limit": 100
        }

        # 模拟数据库查询
        with patch.object(conversation_data_source, '_query_conversations') as mock_query:
            mock_query.return_value = [
                {"id": "conv1", "satisfaction": 4.5, "duration": 300},
                {"id": "conv2", "satisfaction": 3.8, "duration": 450}
            ]

            result = await conversation_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert len(result["conversations"]) == 2
            assert result["conversations"][0]["satisfaction"] == 4.5

    @pytest.mark.asyncio
    async def test_fetch_data_with_messages(self, conversation_data_source):
        """测试包含消息的对话数据获取"""
        config = {
            "include_messages": True,
            "message_limit": 10
        }

        # 模拟数据库查询
        with patch.object(conversation_data_source, '_query_conversations') as mock_query:
            mock_query.return_value = [
                {"id": "conv1", "satisfaction": 4.5}
            ]
            
        with patch.object(conversation_data_source, '_query_messages') as mock_messages:
            mock_messages.return_value = [
                {"id": "msg1", "content": "Hello", "role": "user"},
                {"id": "msg2", "content": "Hi there!", "role": "assistant"}
            ]

            result = await conversation_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert len(result["conversations"]) == 1
            assert "messages" in result["conversations"][0]
            assert len(result["conversations"][0]["messages"]) == 2

    @pytest.mark.asyncio
    async def test_fetch_data_with_filters(self, conversation_data_source):
        """测试带过滤条件的对话数据获取"""
        config = {
            "satisfaction_filter": ">=4.0",
            "duration_filter": ">=300",
            "include_messages": False
        }

        # 模拟数据库查询
        with patch.object(conversation_data_source, '_query_conversations') as mock_query:
            mock_query.return_value = [
                {"id": "conv1", "satisfaction": 4.5, "duration": 350}
            ]

            result = await conversation_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            # 验证过滤条件被应用
            mock_query.assert_called_once()
            call_args = mock_query.call_args[1]
            assert call_args["satisfaction_filter"] == ">=4.0"
            assert call_args["duration_filter"] == ">=300"


class TestQueryDataSource:
    """查询数据源测试类"""

    @pytest.fixture
    def query_data_source(self):
        """创建查询数据源实例"""
        return QueryDataSource(Mock())

    @pytest.mark.asyncio
    async def test_fetch_data_custom_query(self, query_data_source):
        """测试自定义查询数据获取"""
        config = {
            "custom_query": "SELECT COUNT(*) as total FROM sessions WHERE date = %s",
            "query_params": ["2024-01-01"]
        }

        # 模拟数据库查询
        with patch.object(query_data_source, '_execute_query') as mock_execute:
            mock_execute.return_value = [{"total": 250}]

            result = await query_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["query_result"][0]["total"] == 250

    @pytest.mark.asyncio
    async def test_fetch_data_predefined_query(self, query_data_source):
        """测试预定义查询数据获取"""
        config = {
            "query_name": "daily_summary",
            "query_params": {"date": "2024-01-01"}
        }

        # 模拟预定义查询
        with patch.object(query_data_source, '_get_predefined_query') as mock_get_query:
            mock_get_query.return_value = "SELECT * FROM daily_stats WHERE date = %(date)s"
            
        with patch.object(query_data_source, '_execute_query') as mock_execute:
            mock_execute.return_value = [{"sessions": 100, "duration": 300}]

            result = await query_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["query_result"][0]["sessions"] == 100


class TestHumanStatsDataSource:
    """人工统计数据源测试类"""

    @pytest.fixture
    def human_stats_data_source(self):
        """创建人工统计数据源实例"""
        return HumanStatsDataSource(Mock())

    @pytest.mark.asyncio
    async def test_fetch_data_basic(self, human_stats_data_source):
        """测试基础人工统计数据获取"""
        config = {
            "categories": ["satisfaction", "resolution"],
            "include_comments": True
        }

        # 模拟数据库查询
        with patch.object(human_stats_data_source, '_query_human_stats') as mock_query:
            mock_query.return_value = {
                "satisfaction": {"avg": 4.2, "count": 50},
                "resolution": {"resolved": 45, "unresolved": 5},
                "comments": ["Good service", "Need improvement"]
            }

            result = await human_stats_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["satisfaction"]["avg"] == 4.2
            assert result["resolution"]["resolved"] == 45
            assert len(result["comments"]) == 2

    @pytest.mark.asyncio
    async def test_fetch_data_with_aggregation(self, human_stats_data_source):
        """测试带聚合的人工统计数据获取"""
        config = {
            "categories": ["satisfaction"],
            "aggregation": "daily",
            "include_trends": True
        }

        # 模拟数据库查询
        with patch.object(human_stats_data_source, '_query_human_stats') as mock_query:
            mock_query.return_value = {
                "satisfaction": {"avg": 4.1, "count": 30},
                "trends": {
                    "daily": [
                        {"date": "2024-01-01", "avg": 4.1},
                        {"date": "2023-12-31", "avg": 4.0}
                    ]
                }
            }

            result = await human_stats_data_source.fetch_data(
                enterprise_id="test_enterprise",
                device_id="test_device",
                target_date="2024-01-01",
                config=config
            )

            assert result["satisfaction"]["avg"] == 4.1
            assert "trends" in result
            assert len(result["trends"]["daily"]) == 2
