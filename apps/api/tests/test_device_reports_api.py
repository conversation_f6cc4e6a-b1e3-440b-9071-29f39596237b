import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>

from app.routers.device_reports import router
from app.models.report_models import ModuleType, DataSourceType, ExecutionMode


# Create test app
app = FastAPI()
app.include_router(router, prefix="/device-reports")

client = TestClient(app)


class TestDeviceReportsAPI:
    """设备报告API测试类"""

    @pytest.fixture
    def mock_dependencies(self):
        """模拟依赖项"""
        with patch('app.routers.device_reports.get_db') as mock_get_db, \
             patch('app.routers.device_reports.DeviceReportService') as mock_service_class:
            
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            
            yield {
                'db': mock_db,
                'service': mock_service
            }

    def test_create_modular_report_success(self, mock_dependencies):
        """测试创建模块化报告成功"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        request_data = {
            "target_date": "2024-01-01",
            "module_configs": [
                {
                    "module_type": ModuleType.BASIC_USAGE.value,
                    "module_name": "基础使用数据",
                    "module_description": "测试模块",
                    "data_sources": {"statistics": {}},
                    "primary_data_source": DataSourceType.STATISTICS.value,
                    "execution_mode": ExecutionMode.LLM_GENERATE.value,
                    "use_llm": True,
                    "custom_config": {},
                    "prompt_variables": {}
                }
            ]
        }

        # 模拟服务返回
        async def mock_generate():
            yield "报告开始生成", {"type": "start"}
            yield "模块1开始执行", {"type": "module_start", "module_index": 0}
            yield "模块1执行完成", {"type": "module_complete", "module_index": 0}
            yield "报告生成完成", {"type": "complete"}

        mock_dependencies['service'].generate_modular_report = mock_generate

        response = client.post(
            f"/device-reports/{enterprise_id}/{device_id}/modular",
            json=request_data
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    def test_create_modular_report_invalid_data(self, mock_dependencies):
        """测试创建模块化报告时数据无效"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        # 缺少必要字段的请求数据
        request_data = {
            "module_configs": []
        }

        response = client.post(
            f"/device-reports/{enterprise_id}/{device_id}/modular",
            json=request_data
        )

        assert response.status_code == 422  # Validation error

    def test_get_report_modules_success(self, mock_dependencies):
        """测试获取报告模块成功"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        report_id = "test_report_123"

        # 模拟服务返回
        mock_modules = [
            {
                "id": 1,
                "module_type": ModuleType.BASIC_USAGE.value,
                "module_name": "基础使用数据",
                "status": "completed",
                "execution_time": 2.5,
                "content": "测试内容"
            }
        ]
        
        mock_dependencies['service'].report_data_service.get_report_modules.return_value = mock_modules

        response = client.get(
            f"/device-reports/{enterprise_id}/{device_id}/modules",
            params={"report_id": report_id}
        )

        assert response.status_code == 200
        data = response.json()
        assert len(data["modules"]) == 1
        assert data["modules"][0]["module_name"] == "基础使用数据"

    def test_get_report_modules_not_found(self, mock_dependencies):
        """测试获取不存在的报告模块"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        report_id = "nonexistent_report"

        # 模拟服务返回空列表
        mock_dependencies['service'].report_data_service.get_report_modules.return_value = []

        response = client.get(
            f"/device-reports/{enterprise_id}/{device_id}/modules",
            params={"report_id": report_id}
        )

        assert response.status_code == 404

    def test_get_module_templates_success(self, mock_dependencies):
        """测试获取模块模板成功"""
        # 模拟服务返回
        mock_templates = [
            {
                "id": 1,
                "module_type": ModuleType.BASIC_USAGE.value,
                "template_name": "基础使用数据模板",
                "description": "用于生成基础使用数据的模板",
                "default_config": {"metrics": ["session_count"]},
                "is_active": True
            }
        ]
        
        mock_dependencies['service'].report_data_service.get_module_templates.return_value = mock_templates

        response = client.get("/device-reports/templates")

        assert response.status_code == 200
        data = response.json()
        assert len(data["templates"]) == 1
        assert data["templates"][0]["template_name"] == "基础使用数据模板"

    def test_get_data_sources_success(self, mock_dependencies):
        """测试获取数据源配置成功"""
        # 模拟服务返回
        mock_data_sources = [
            {
                "id": 1,
                "source_type": DataSourceType.STATISTICS.value,
                "source_name": "统计数据源",
                "description": "提供基础统计数据",
                "connection_config": {"database": "statistics_db"},
                "is_active": True
            }
        ]
        
        mock_dependencies['service'].report_data_service.get_data_source_configs.return_value = mock_data_sources

        response = client.get("/device-reports/data-sources")

        assert response.status_code == 200
        data = response.json()
        assert len(data["data_sources"]) == 1
        assert data["data_sources"][0]["source_name"] == "统计数据源"

    def test_query_data_source_success(self, mock_dependencies):
        """测试查询数据源成功"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        request_data = {
            "data_source_type": DataSourceType.STATISTICS.value,
            "query_config": {
                "metrics": ["session_count", "avg_duration"],
                "time_range": "1d"
            },
            "target_date": "2024-01-01"
        }

        # 模拟服务返回
        mock_result = {
            "session_count": 150,
            "avg_duration": 280,
            "query_time": "2024-01-01T10:00:00Z"
        }
        
        mock_dependencies['service'].unified_data_service.fetch_data = AsyncMock(return_value=mock_result)

        response = client.post(
            f"/device-reports/{enterprise_id}/{device_id}/data-query",
            json=request_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["result"]["session_count"] == 150
        assert data["result"]["avg_duration"] == 280

    def test_query_data_source_invalid_config(self, mock_dependencies):
        """测试查询数据源时配置无效"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        # 缺少必要字段的请求数据
        request_data = {
            "query_config": {"metrics": ["session_count"]}
            # 缺少 data_source_type
        }

        response = client.post(
            f"/device-reports/{enterprise_id}/{device_id}/data-query",
            json=request_data
        )

        assert response.status_code == 422  # Validation error

    def test_get_data_source_schemas_success(self, mock_dependencies):
        """测试获取数据源架构成功"""
        # 模拟服务返回
        mock_schemas = {
            DataSourceType.STATISTICS.value: {
                "metrics": ["session_count", "avg_duration", "total_duration"],
                "filters": ["date_range", "device_type"],
                "aggregations": ["hourly", "daily", "weekly"]
            },
            DataSourceType.CONVERSATION.value: {
                "fields": ["session_id", "satisfaction", "duration"],
                "filters": ["satisfaction_range", "duration_range"],
                "includes": ["messages", "metadata"]
            }
        }
        
        mock_dependencies['service'].unified_data_service.get_data_source_schemas.return_value = mock_schemas

        response = client.get("/device-reports/data-sources/schemas")

        assert response.status_code == 200
        data = response.json()
        assert DataSourceType.STATISTICS.value in data["schemas"]
        assert "metrics" in data["schemas"][DataSourceType.STATISTICS.value]

    def test_get_module_types_success(self, mock_dependencies):
        """测试获取模块类型成功"""
        response = client.get("/device-reports/module-types")

        assert response.status_code == 200
        data = response.json()
        assert "module_types" in data
        
        # 验证所有模块类型都被返回
        expected_types = [module_type.value for module_type in ModuleType]
        returned_types = [mt["value"] for mt in data["module_types"]]
        
        for expected_type in expected_types:
            assert expected_type in returned_types

    @pytest.mark.asyncio
    async def test_sse_stream_format(self, mock_dependencies):
        """测试SSE流格式"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        request_data = {
            "target_date": "2024-01-01",
            "module_configs": []
        }

        # 模拟服务返回
        async def mock_generate():
            yield "开始生成", {"type": "start", "timestamp": "2024-01-01T10:00:00Z"}
            yield "生成完成", {"type": "complete", "timestamp": "2024-01-01T10:05:00Z"}

        mock_dependencies['service'].generate_modular_report = mock_generate

        with client.stream("POST", f"/device-reports/{enterprise_id}/{device_id}/modular", json=request_data) as response:
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
            
            # 读取流数据
            content = response.read().decode()
            
            # 验证SSE格式
            assert "data: " in content
            assert "开始生成" in content
            assert "生成完成" in content

    def test_error_handling_service_exception(self, mock_dependencies):
        """测试服务异常处理"""
        enterprise_id = "test_enterprise"
        device_id = "test_device"
        
        request_data = {
            "target_date": "2024-01-01",
            "module_configs": []
        }

        # 模拟服务抛出异常
        async def mock_generate():
            raise Exception("服务内部错误")

        mock_dependencies['service'].generate_modular_report = mock_generate

        response = client.post(
            f"/device-reports/{enterprise_id}/{device_id}/modular",
            json=request_data
        )

        # 应该返回错误信息
        assert response.status_code == 500

    def test_parameter_validation(self, mock_dependencies):
        """测试参数验证"""
        # 测试无效的enterprise_id
        response = client.post(
            "/device-reports//test_device/modular",
            json={"target_date": "2024-01-01", "module_configs": []}
        )
        assert response.status_code == 404

        # 测试无效的device_id
        response = client.post(
            "/device-reports/test_enterprise//modular",
            json={"target_date": "2024-01-01", "module_configs": []}
        )
        assert response.status_code == 404

    def test_cors_headers(self, mock_dependencies):
        """测试CORS头部"""
        response = client.options("/device-reports/test_enterprise/test_device/modular")
        
        # 检查CORS相关头部（如果配置了的话）
        # 这取决于应用的CORS配置
