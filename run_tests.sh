#!/bin/bash

# 模块化报告系统测试运行脚本
# 用于运行后端和前端的所有测试用例

set -e

echo "🚀 开始运行模块化报告系统测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "错误: $1 命令未找到，请先安装"
        exit 1
    fi
}

# 函数：运行后端测试
run_backend_tests() {
    print_message $BLUE "📋 运行后端测试..."
    
    cd apps/api
    
    # 检查pytest是否安装
    if ! python -m pytest --version &> /dev/null; then
        print_message $YELLOW "安装pytest和相关依赖..."
        pip install pytest pytest-asyncio pytest-cov httpx
    fi
    
    # 运行测试
    print_message $BLUE "运行模块化报告服务测试..."
    python -m pytest tests/test_modular_report_service.py -v --cov=app.services.device_report_service
    
    print_message $BLUE "运行统一数据服务测试..."
    python -m pytest tests/test_unified_data_service.py -v --cov=app.services.unified_data_service
    
    print_message $BLUE "运行API路由测试..."
    python -m pytest tests/test_device_reports_api.py -v --cov=app.routers.device_reports
    
    # 生成覆盖率报告
    print_message $BLUE "生成测试覆盖率报告..."
    python -m pytest tests/ --cov=app --cov-report=html --cov-report=term-missing
    
    cd ../..
    print_message $GREEN "✅ 后端测试完成"
}

# 函数：运行前端测试
run_frontend_tests() {
    print_message $BLUE "🎨 运行前端测试..."
    
    cd apps/web
    
    # 检查npm/yarn是否存在
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
    else
        print_message $RED "错误: 未找到npm或yarn"
        exit 1
    fi
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "安装前端依赖..."
        $PACKAGE_MANAGER install
    fi
    
    # 检查vitest是否安装
    if ! $PACKAGE_MANAGER list vitest &> /dev/null; then
        print_message $YELLOW "安装测试依赖..."
        if [ "$PACKAGE_MANAGER" = "yarn" ]; then
            yarn add -D vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom
        else
            npm install -D vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom
        fi
    fi
    
    # 运行测试
    print_message $BLUE "运行模块化报告页面测试..."
    $PACKAGE_MANAGER run test src/tests/ModularReportPage.test.tsx
    
    print_message $BLUE "运行图表组件测试..."
    $PACKAGE_MANAGER run test src/tests/ChartDisplayPanel.test.tsx
    
    # 运行所有测试并生成覆盖率报告
    print_message $BLUE "运行所有前端测试..."
    $PACKAGE_MANAGER run test:coverage
    
    cd ../..
    print_message $GREEN "✅ 前端测试完成"
}

# 函数：运行集成测试
run_integration_tests() {
    print_message $BLUE "🔗 运行集成测试..."
    
    # 启动后端服务（如果需要）
    print_message $YELLOW "准备集成测试环境..."
    
    cd apps/api
    
    # 运行集成测试
    python -m pytest tests/ -m integration -v
    
    cd ../..
    print_message $GREEN "✅ 集成测试完成"
}

# 函数：生成测试报告
generate_test_report() {
    print_message $BLUE "📊 生成测试报告..."
    
    # 创建报告目录
    mkdir -p test_reports
    
    # 合并覆盖率报告
    echo "# 模块化报告系统测试报告" > test_reports/README.md
    echo "" >> test_reports/README.md
    echo "## 测试概览" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "- 后端测试: ✅ 通过" >> test_reports/README.md
    echo "- 前端测试: ✅ 通过" >> test_reports/README.md
    echo "- 集成测试: ✅ 通过" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "## 覆盖率报告" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "- 后端覆盖率报告: [htmlcov/index.html](../apps/api/htmlcov/index.html)" >> test_reports/README.md
    echo "- 前端覆盖率报告: [coverage/index.html](../apps/web/coverage/index.html)" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "## 测试文件" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "### 后端测试" >> test_reports/README.md
    echo "- \`apps/api/tests/test_modular_report_service.py\` - 模块化报告服务测试" >> test_reports/README.md
    echo "- \`apps/api/tests/test_unified_data_service.py\` - 统一数据服务测试" >> test_reports/README.md
    echo "- \`apps/api/tests/test_device_reports_api.py\` - API路由测试" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "### 前端测试" >> test_reports/README.md
    echo "- \`apps/web/src/tests/ModularReportPage.test.tsx\` - 模块化报告页面测试" >> test_reports/README.md
    echo "- \`apps/web/src/tests/ChartDisplayPanel.test.tsx\` - 图表组件测试" >> test_reports/README.md
    echo "" >> test_reports/README.md
    echo "生成时间: $(date)" >> test_reports/README.md
    
    print_message $GREEN "✅ 测试报告已生成: test_reports/README.md"
}

# 主函数
main() {
    # 解析命令行参数
    case "${1:-all}" in
        "backend")
            run_backend_tests
            ;;
        "frontend")
            run_frontend_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "all")
            run_backend_tests
            run_frontend_tests
            run_integration_tests
            generate_test_report
            ;;
        "help")
            echo "用法: $0 [backend|frontend|integration|all|help]"
            echo ""
            echo "选项:"
            echo "  backend     - 只运行后端测试"
            echo "  frontend    - 只运行前端测试"
            echo "  integration - 只运行集成测试"
            echo "  all         - 运行所有测试 (默认)"
            echo "  help        - 显示此帮助信息"
            exit 0
            ;;
        *)
            print_message $RED "错误: 未知选项 '$1'"
            echo "使用 '$0 help' 查看可用选项"
            exit 1
            ;;
    esac
    
    print_message $GREEN "🎉 所有测试完成！"
}

# 运行主函数
main "$@"
