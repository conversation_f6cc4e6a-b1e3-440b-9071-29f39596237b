# 模块化报告系统快速开始指南

## 🚀 5分钟快速体验

本指南帮助您快速启动和体验模块化报告系统的核心功能。

## 前置条件

确保您的系统已安装：
- Python 3.8+
- Node.js 16.0+
- MySQL 8.0+
- Git

## 快速启动步骤

### 1. 获取代码
```bash
cd /data/app/CEMonitor
```

### 2. 数据库快速设置
```bash
# 连接MySQL并创建数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS agentos_online;"

# 执行模块化报告表创建
cd apps/api
mysql -u root -p agentos_online < create_modular_report_tables.sql
```

### 3. 后端快速启动
```bash
cd apps/api

# 创建虚拟环境（如果不存在）
python -m venv venv
source venv/bin/activate  # Linux/macOS

# 安装核心依赖
pip install fastapi uvicorn sqlalchemy pymysql python-multipart

# 创建基础配置文件
cat > .env << EOF
DATABASE_URL_AGENTOS=mysql+pymysql://root:your_password@localhost:3306/agentos_online
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
EOF

# 启动服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 前端快速启动
```bash
# 新开终端
cd apps/web

# 安装依赖
npm install

# 创建配置文件
cat > .env << EOF
VITE_API_BASE_URL=http://localhost:8000
VITE_ENABLE_MODULAR_REPORTS=true
EOF

# 启动开发服务器
npm run dev
```

### 5. 验证安装
打开浏览器访问：
- 前端页面: http://localhost:5173
- API文档: http://localhost:8000/docs

## 🎯 快速功能测试

### 测试1: API接口验证
```bash
# 检查模块类型
curl http://localhost:8000/device-reports/module-types

# 检查数据源
curl http://localhost:8000/device-reports/data-sources

# 检查模板
curl http://localhost:8000/device-reports/templates
```

### 测试2: 创建简单报告
```bash
curl -X POST "http://localhost:8000/device-reports/demo_enterprise/demo_device/modular" \
  -H "Content-Type: application/json" \
  -d '{
    "target_date": "2024-01-31",
    "module_configs": [
      {
        "module_type": "basic_usage",
        "module_name": "基础使用数据测试",
        "module_description": "快速测试模块",
        "data_sources": {"statistics": {"metrics": ["session_count"]}},
        "primary_data_source": "statistics",
        "execution_mode": "direct_output",
        "use_llm": false,
        "custom_config": {},
        "prompt_variables": {}
      }
    ]
  }'
```

### 测试3: 前端界面测试
1. 访问 http://localhost:5173
2. 导航到模块化报告页面
3. 填写基本信息：
   - 企业ID: `demo_enterprise`
   - 设备ID: `demo_device`
   - 目标日期: `2024-01-31`
4. 添加一个基础使用数据模块
5. 点击"开始生成报告"

## 🧪 运行测试

### 快速测试
```bash
# 运行后端核心测试
cd apps/api
python -m pytest tests/test_modular_report_service.py::TestModularReportService::test_generate_modular_report_success -v

# 运行前端核心测试
cd apps/web
npm test src/tests/ModularReportPage.test.tsx
```

### 完整测试
```bash
# 从项目根目录运行
./run_tests.sh
```

## 📊 示例数据

### 创建测试数据
```sql
-- 连接到数据库
mysql -u root -p agentos_online

-- 插入示例设备报告
INSERT INTO device_report (
  enterprise_id, device_id, target_date, report_type, 
  is_modular, total_modules, completed_modules,
  created_at, updated_at
) VALUES (
  'demo_enterprise', 'demo_device', '2024-01-31', 'daily',
  1, 1, 0,
  NOW(), NOW()
);

-- 插入示例模块
INSERT INTO report_module (
  report_id, module_type, module_name, module_description,
  data_sources, primary_data_source, execution_mode,
  status, created_at, updated_at
) VALUES (
  LAST_INSERT_ID(), 'basic_usage', '基础使用数据', '示例模块',
  '{"statistics": {"metrics": ["session_count"]}}', 'statistics', 'direct_output',
  'pending', NOW(), NOW()
);
```

## 🔧 常见问题快速解决

### 问题1: 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 重启MySQL服务
sudo systemctl restart mysql

# 检查连接
mysql -u root -p -e "SELECT 1;"
```

### 问题2: 端口被占用
```bash
# 检查端口使用情况
lsof -i :8000  # 后端端口
lsof -i :5173  # 前端端口

# 杀死占用进程
kill -9 <PID>
```

### 问题3: 依赖安装失败
```bash
# 更新pip
pip install --upgrade pip

# 清除npm缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 问题4: 前端页面空白
1. 检查浏览器控制台错误
2. 验证API服务是否运行
3. 检查网络请求状态
4. 清除浏览器缓存

## 📝 下一步

### 深入了解
1. 阅读 [功能说明文档](MODULAR_REPORT_FEATURES.md)
2. 查看 [完整部署指南](DEPLOYMENT_GUIDE.md)
3. 了解 [修改清单](MODULAR_REPORT_CHANGES.md)

### 自定义配置
1. 配置真实的数据库连接
2. 设置LLM API密钥
3. 自定义模块模板
4. 配置数据源

### 生产部署
1. 使用生产级数据库
2. 配置反向代理
3. 启用HTTPS
4. 设置监控和日志

## 🆘 获取帮助

### 日志查看
```bash
# 后端日志
tail -f logs/app.log

# 前端开发服务器日志
# 查看终端输出

# 数据库日志
tail -f /var/log/mysql/error.log
```

### 调试模式
```bash
# 后端调试模式
export DEBUG=true
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

# 前端调试模式
export VITE_DEBUG=true
npm run dev
```

### 重置环境
```bash
# 重置数据库
mysql -u root -p -e "DROP DATABASE agentos_online; CREATE DATABASE agentos_online;"
mysql -u root -p agentos_online < apps/api/create_modular_report_tables.sql

# 重置后端环境
cd apps/api
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 重置前端环境
cd apps/web
rm -rf node_modules package-lock.json
npm install
```

## 🎉 成功标志

如果您看到以下内容，说明系统运行正常：

1. **后端API**: 访问 http://localhost:8000/docs 看到Swagger文档
2. **前端页面**: 访问 http://localhost:5173 看到CEMonitor界面
3. **数据库**: 能够查询到模块模板和数据源配置
4. **测试**: 所有核心测试通过
5. **功能**: 能够成功创建和查看模块化报告

恭喜！您已经成功启动了模块化报告系统。现在可以开始探索更多高级功能了。
