# 模块化报告系统功能说明

## 系统概述

模块化报告系统是CEMonitor项目的核心功能升级，将原有的单一LLM报告生成转换为支持5个独立模块的分步执行系统。该系统提供了灵活的配置选项、多种数据源支持、实时进度跟踪和丰富的交互功能。

## 核心功能

### 1. 五大报告模块

#### 1.1 基础使用数据模块 (BASIC_USAGE)
**功能描述**: 分析设备的基础使用情况、当天数据趋势和action数据统计

**数据来源**:
- 统计数据库中的会话数据
- 设备使用时长统计
- 用户行为action记录

**输出内容**:
- 会话总数和趋势分析
- 平均会话时长
- 高峰使用时段分析
- Action类型分布统计
- 用户活跃度指标

**支持的展示方式**:
- 文本报告
- 趋势图表（线图、柱状图）
- 数据表格

#### 1.2 事件分析模块 (EVENT_ANALYSIS)
**功能描述**: 识别和分析当日的亮点事件和预警事件

**数据来源**:
- 系统日志数据
- 异常事件记录
- 用户反馈数据

**输出内容**:
- 亮点事件识别和分析
- 预警事件统计和分类
- 事件影响范围评估
- 处理建议和优先级

**支持的展示方式**:
- 事件时间线
- 分类统计图表
- 影响范围热力图

#### 1.3 服务质量模块 (SERVICE_QUALITY)
**功能描述**: 分析当日满意和不满意的服务案例

**数据来源**:
- 用户满意度评分
- 服务质量评估数据
- 客服对话记录

**输出内容**:
- 满意度分布统计
- 典型满意案例分析
- 不满意案例原因分析
- 服务质量改进建议

**支持的展示方式**:
- 满意度分布饼图
- 案例详情列表
- 质量趋势分析

#### 1.4 洞察分析模块 (INSIGHT_ANALYSIS)
**功能描述**: 从新视角观察和分析数据，发现潜在的模式和趋势

**数据来源**:
- 多维度数据交叉分析
- 历史数据对比
- 外部数据关联

**输出内容**:
- 数据模式识别
- 异常趋势发现
- 用户行为洞察
- 业务机会识别

**支持的展示方式**:
- 多维度分析图表
- 相关性分析
- 预测模型结果

#### 1.5 业务建议模块 (BUSINESS_ADVICE)
**功能描述**: 基于数据分析结果提出具体的经营建议

**数据来源**:
- 综合前四个模块的分析结果
- 业务规则和最佳实践
- 行业基准数据

**输出内容**:
- 具体可执行的建议
- 优先级排序
- 预期效果评估
- 实施时间规划

**支持的展示方式**:
- 建议列表
- 优先级矩阵
- 效果预测图表

### 2. 多数据源支持

#### 2.1 统计数据源 (STATISTICS)
- **描述**: 从统计数据库获取结构化数据
- **支持的查询**: 会话统计、用户行为、设备使用情况
- **数据格式**: 数值型统计数据、时间序列数据

#### 2.2 对话数据源 (CONVERSATION)
- **描述**: 从对话系统获取会话记录和消息内容
- **支持的查询**: 会话详情、消息内容、满意度评分
- **数据格式**: 文本数据、结构化会话信息

#### 2.3 查询数据源 (QUERY)
- **描述**: 支持自定义SQL查询和预定义查询模板
- **支持的查询**: 灵活的数据查询、复杂的关联分析
- **数据格式**: 任意结构的查询结果

#### 2.4 人工统计数据源 (HUMAN_STATS)
- **描述**: 人工录入的统计数据和评估结果
- **支持的查询**: 人工评估、专家分析、外部数据
- **数据格式**: 人工标注数据、评估结果

#### 2.5 混合数据源 (MIXED)
- **描述**: 组合多个数据源的数据进行综合分析
- **支持的查询**: 跨数据源关联、权重配置
- **数据格式**: 多源数据融合结果

### 3. 四种执行模式

#### 3.1 直接输出模式 (DIRECT_OUTPUT)
- **描述**: 直接输出处理后的数据，不经过额外的格式化
- **适用场景**: 简单的数据展示、快速查看结果
- **输出格式**: 结构化文本、JSON数据

#### 3.2 模板渲染模式 (TEMPLATE_RENDER)
- **描述**: 使用预定义模板格式化输出内容
- **适用场景**: 标准化报告、格式统一的输出
- **输出格式**: HTML、Markdown、自定义格式

#### 3.3 图表展示模式 (CHART_DISPLAY)
- **描述**: 将数据转换为图表配置，支持多种图表类型
- **适用场景**: 数据可视化、趋势分析
- **支持的图表**: 线图、柱状图、饼图、散点图、面积图

#### 3.4 LLM生成模式 (LLM_GENERATE)
- **描述**: 使用大语言模型生成自然语言报告
- **适用场景**: 智能分析、个性化报告
- **输出格式**: 自然语言文本、结构化分析

### 4. 实时进度跟踪

#### 4.1 Server-Sent Events (SSE) 流
- **实时连接**: 建立持久连接，实时推送进度信息
- **状态更新**: 模块开始、进行中、完成、错误等状态
- **内容流**: 实时推送生成的内容片段

#### 4.2 进度可视化
- **模块状态**: 每个模块的执行状态可视化
- **进度条**: 整体进度和单个模块进度
- **时间跟踪**: 执行时间和预估剩余时间

#### 4.3 错误处理
- **错误捕获**: 实时捕获和报告执行错误
- **重试机制**: 支持失败模块的重新执行
- **错误详情**: 详细的错误信息和解决建议

### 5. 交互功能

#### 5.1 图表交互
- **数据点点击**: 点击图表数据点查看详细信息
- **会话详情**: 从图表直接跳转到相关会话详情
- **缩放平移**: 支持图表的缩放和平移操作
- **全屏显示**: 图表全屏查看功能

#### 5.2 会话详情查看
- **会话信息**: 完整的会话基础信息展示
- **对话记录**: 详细的消息列表和内容
- **统计指标**: 会话相关的统计数据
- **导出功能**: 支持会话数据的导出

#### 5.3 配置管理
- **模块配置**: 灵活的模块参数配置
- **模板管理**: 模块模板的保存和加载
- **数据源配置**: 数据源参数的自定义设置

### 6. 高级功能

#### 6.1 模板系统
- **预定义模板**: 每种模块类型的默认模板
- **自定义模板**: 支持用户自定义模板
- **模板变量**: 动态变量替换和参数化

#### 6.2 缓存机制
- **数据缓存**: 数据查询结果缓存
- **模板缓存**: 模板编译结果缓存
- **配置缓存**: 配置信息缓存

#### 6.3 性能优化
- **异步处理**: 模块并行执行支持
- **资源管理**: 内存和CPU资源优化
- **查询优化**: 数据库查询性能优化

## 用户界面

### 1. 主界面
- **配置面板**: 模块配置和参数设置
- **进度跟踪**: 实时进度显示和状态监控
- **结果展示**: 报告内容和图表展示区域

### 2. 配置界面
- **模块选择**: 选择需要执行的模块
- **数据源配置**: 配置每个模块的数据源
- **执行模式**: 选择模块的执行模式
- **参数设置**: 自定义模块参数

### 3. 结果界面
- **报告内容**: 生成的报告文本内容
- **图表展示**: 交互式图表和可视化
- **导出功能**: 支持多种格式的导出

## API接口

### 1. 报告生成接口
- `POST /device-reports/{enterprise_id}/{device_id}/modular`: 创建模块化报告
- `GET /device-reports/{enterprise_id}/{device_id}/modules`: 获取报告模块

### 2. 配置管理接口
- `GET /device-reports/templates`: 获取模块模板
- `GET /device-reports/data-sources`: 获取数据源配置
- `GET /device-reports/module-types`: 获取模块类型

### 3. 数据查询接口
- `POST /device-reports/{enterprise_id}/{device_id}/data-query`: 查询数据源
- `GET /device-reports/data-sources/schemas`: 获取数据源架构

## 扩展性

### 1. 模块扩展
- **新模块类型**: 支持添加新的模块类型
- **自定义逻辑**: 支持自定义模块处理逻辑
- **插件机制**: 支持第三方模块插件

### 2. 数据源扩展
- **新数据源**: 支持添加新的数据源类型
- **自定义连接**: 支持自定义数据源连接
- **数据转换**: 支持自定义数据转换逻辑

### 3. 输出格式扩展
- **新格式**: 支持添加新的输出格式
- **自定义模板**: 支持自定义输出模板
- **格式转换**: 支持多种格式之间的转换

## 安全性

### 1. 数据安全
- **访问控制**: 基于角色的数据访问控制
- **数据脱敏**: 敏感数据的自动脱敏处理
- **审计日志**: 完整的操作审计日志

### 2. 接口安全
- **认证授权**: API接口的认证和授权
- **请求限制**: 防止恶意请求和DDoS攻击
- **输入验证**: 严格的输入参数验证

### 3. 系统安全
- **错误处理**: 安全的错误信息处理
- **日志安全**: 敏感信息的日志过滤
- **配置安全**: 安全的配置信息管理

## 监控和维护

### 1. 性能监控
- **执行时间**: 模块执行时间监控
- **资源使用**: CPU和内存使用监控
- **并发处理**: 并发请求处理监控

### 2. 错误监控
- **错误统计**: 错误类型和频率统计
- **异常告警**: 异常情况的实时告警
- **故障恢复**: 自动故障恢复机制

### 3. 数据质量
- **数据验证**: 数据质量验证和检查
- **一致性检查**: 数据一致性验证
- **完整性保证**: 数据完整性保证机制
