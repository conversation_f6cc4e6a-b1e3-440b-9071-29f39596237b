# CEMonitor 模块化报告系统

## 🎯 项目概述

CEMonitor模块化报告系统是对原有设备报告生成功能的全面升级，将单一的LLM报告生成转换为支持5个独立模块的分步执行系统。该系统提供了灵活的配置选项、多种数据源支持、实时进度跟踪和丰富的交互功能。

## ✨ 核心特性

### 🔧 模块化架构
- **5个独立模块**: 基础使用数据、事件分析、服务质量、洞察分析、业务建议
- **分步执行**: 支持模块的独立配置和执行
- **灵活组合**: 可根据需求选择和组合不同模块

### 📊 多数据源支持
- **统计数据源**: 结构化统计数据查询
- **对话数据源**: 会话记录和消息内容
- **查询数据源**: 自定义SQL查询支持
- **人工统计**: 人工录入数据支持
- **混合数据源**: 多源数据融合分析

### 🎨 多种执行模式
- **直接输出**: 原始数据直接展示
- **模板渲染**: 使用预定义模板格式化
- **图表展示**: 数据可视化图表生成
- **LLM生成**: 智能自然语言报告

### 📈 实时进度跟踪
- **SSE流**: Server-Sent Events实时推送
- **进度可视化**: 模块执行状态实时显示
- **错误处理**: 实时错误捕获和重试机制

### 🖱️ 丰富交互功能
- **图表交互**: 数据点点击查看详情
- **会话详情**: 从图表直接跳转会话信息
- **导出功能**: 多格式数据导出支持
- **配置管理**: 灵活的模块和数据源配置

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 Web 应用  │    │   后端 API 服务  │    │   数据库集群     │
│                │    │                │    │                │
│ • React + TS   │◄──►│ • FastAPI      │◄──►│ • MySQL        │
│ • ECharts      │    │ • SQLAlchemy   │    │ • Redis (可选)  │
│ • SSE Client   │    │ • Async/Await  │    │ • 多数据源      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件
- **统一数据服务**: 多数据源统一访问接口
- **模块化报告服务**: 核心报告生成逻辑
- **模板系统**: 可重用的模块配置模板
- **图表引擎**: ECharts集成的可视化系统
- **实时通信**: SSE实时进度推送

## 📁 项目结构

```
CEMonitor/
├── apps/
│   ├── api/                          # 后端API服务
│   │   ├── app/
│   │   │   ├── models/
│   │   │   │   └── report_models.py  # 数据模型定义
│   │   │   ├── services/
│   │   │   │   ├── unified_data_service.py      # 统一数据服务
│   │   │   │   ├── device_report_service.py     # 报告生成服务
│   │   │   │   ├── report_data_service.py       # 报告数据服务
│   │   │   │   └── modular_report_helpers.py    # 模块化助手
│   │   │   └── routers/
│   │   │       └── device_reports.py # API路由定义
│   │   ├── tests/                    # 后端测试
│   │   ├── create_modular_report_tables.sql     # 数据库迁移脚本
│   │   └── pytest.ini               # 测试配置
│   └── web/                          # 前端Web应用
│       ├── src/
│       │   ├── pages/
│       │   │   └── ModularReportPage.tsx        # 主页面
│       │   ├── components/
│       │   │   ├── ModuleProgressTracker.tsx    # 进度跟踪
│       │   │   ├── ModuleConfigPanel.tsx        # 配置面板
│       │   │   ├── ChartDisplayPanel.tsx        # 图表展示
│       │   │   ├── SessionDetailModal.tsx       # 会话详情
│       │   │   ├── EnhancedChartComponent.tsx   # 增强图表
│       │   │   └── ChartGallery.tsx             # 图表集合
│       │   └── tests/                # 前端测试
│       └── vitest.config.ts          # 测试配置
├── run_tests.sh                      # 测试运行脚本
├── MODULAR_REPORT_CHANGES.md         # 修改清单
├── MODULAR_REPORT_FEATURES.md        # 功能说明
├── DEPLOYMENT_GUIDE.md               # 部署指南
├── QUICK_START.md                    # 快速开始
└── README_MODULAR_REPORTS.md         # 本文档
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.8+
- Node.js 16.0+
- MySQL 8.0+

### 2. 快速启动
```bash
# 1. 数据库设置
mysql -u root -p -e "CREATE DATABASE agentos_online;"
cd apps/api && mysql -u root -p agentos_online < create_modular_report_tables.sql

# 2. 启动后端
cd apps/api
python -m venv venv && source venv/bin/activate
pip install fastapi uvicorn sqlalchemy pymysql
uvicorn app.main:app --reload

# 3. 启动前端
cd apps/web
npm install && npm run dev
```

### 3. 验证安装
- 前端: http://localhost:5173
- API文档: http://localhost:8000/docs

详细步骤请参考 [快速开始指南](QUICK_START.md)

## 📖 文档导航

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [快速开始](QUICK_START.md) | 5分钟快速体验系统 | 所有用户 |
| [功能说明](MODULAR_REPORT_FEATURES.md) | 详细功能介绍和使用说明 | 产品经理、用户 |
| [部署指南](DEPLOYMENT_GUIDE.md) | 完整的部署和测试指南 | 运维工程师 |
| [修改清单](MODULAR_REPORT_CHANGES.md) | 详细的代码修改记录 | 开发工程师 |

## 🧪 测试

### 自动化测试
```bash
# 运行所有测试
./run_tests.sh

# 分别运行
./run_tests.sh backend    # 后端测试
./run_tests.sh frontend   # 前端测试
./run_tests.sh integration # 集成测试
```

### 测试覆盖率
- 后端测试覆盖率: 80%+
- 前端测试覆盖率: 80%+
- 集成测试覆盖: 核心功能流程

## 🔧 配置说明

### 后端配置 (.env)
```env
# 数据库配置
DATABASE_URL_AGENTOS=mysql+pymysql://user:pass@localhost:3306/agentos_online
DATABASE_URL_SPEECH=mysql+pymysql://user:pass@localhost:3306/speech_ai_robot
DATABASE_URL_BIGDATA=mysql+pymysql://user:pass@localhost:3306/bigdata_cn

# LLM配置
LLM_API_KEY=your_api_key
LLM_BASE_URL=https://api.your-provider.com

# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false
```

### 前端配置 (.env)
```env
# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MODULAR_REPORTS=true
VITE_ENABLE_CHART_EXPORT=true
```

## 📊 API接口

### 核心接口
- `POST /device-reports/{enterprise_id}/{device_id}/modular` - 创建模块化报告
- `GET /device-reports/{enterprise_id}/{device_id}/modules` - 获取报告模块
- `GET /device-reports/templates` - 获取模块模板
- `GET /device-reports/data-sources` - 获取数据源配置
- `POST /device-reports/{enterprise_id}/{device_id}/data-query` - 查询数据源

完整API文档: http://localhost:8000/docs

## 🎯 使用场景

### 1. 日常运营报告
- 配置基础使用数据模块
- 选择统计数据源
- 使用图表展示模式
- 定期生成运营报告

### 2. 问题分析报告
- 配置事件分析模块
- 选择对话数据源
- 使用LLM生成模式
- 深度分析问题原因

### 3. 服务质量评估
- 配置服务质量模块
- 选择混合数据源
- 使用模板渲染模式
- 生成质量评估报告

### 4. 业务洞察分析
- 配置洞察分析模块
- 选择查询数据源
- 使用LLM生成模式
- 发现业务机会

## 🔄 扩展性

### 新增模块类型
1. 在 `ModuleType` 枚举中添加新类型
2. 在 `modular_report_helpers.py` 中实现处理逻辑
3. 在数据库中添加对应模板
4. 在前端添加配置界面

### 新增数据源
1. 在 `DataSourceType` 枚举中添加新类型
2. 在 `unified_data_service.py` 中实现数据源类
3. 在数据库中添加配置记录
4. 在前端添加配置选项

### 新增执行模式
1. 在 `ExecutionMode` 枚举中添加新模式
2. 在 `device_report_service.py` 中实现执行逻辑
3. 在前端添加模式选择

## 🛡️ 安全性

### 数据安全
- 输入验证和SQL注入防护
- 敏感数据脱敏处理
- 访问控制和权限管理

### API安全
- 请求频率限制
- 认证和授权机制
- 错误信息过滤

### 前端安全
- XSS和CSRF防护
- 安全的数据传输
- 客户端数据验证

## 📈 性能优化

### 数据库优化
- 索引策略优化
- 查询性能调优
- 连接池配置

### API优化
- 异步处理支持
- 响应缓存策略
- 资源使用优化

### 前端优化
- 组件懒加载
- 图表性能优化
- 状态管理优化

## 🤝 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 代码规范
- 后端: PEP 8 Python代码规范
- 前端: ESLint + Prettier
- 测试: 保持80%+覆盖率

### 提交规范
- feat: 新功能
- fix: 错误修复
- docs: 文档更新
- test: 测试相关
- refactor: 代码重构

## 📞 支持与反馈

### 问题报告
- 通过GitHub Issues报告问题
- 提供详细的错误信息和复现步骤
- 包含系统环境信息

### 功能建议
- 通过GitHub Discussions讨论新功能
- 描述使用场景和预期效果
- 考虑向后兼容性

### 技术支持
- 查看文档和FAQ
- 搜索已有的Issues
- 联系开发团队

## 📄 许可证

本项目采用 MIT 许可证，详情请参考 LICENSE 文件。

## 🙏 致谢

感谢所有为CEMonitor模块化报告系统做出贡献的开发者和用户。

---

**CEMonitor模块化报告系统** - 让数据分析更智能，让报告生成更灵活！
